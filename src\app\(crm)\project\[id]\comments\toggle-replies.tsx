'use client';

import { useState, useEffect } from 'react';

import { Form } from '@/lib/form';

import { ReplyForm } from './reply-form';
import { DeleteButton } from './delete-button';

// Create a global store to persist toggle states across page refreshes
interface CommentState {
  [key: string]: boolean; // commentId -> isOpen
}

// Use localStorage to persist toggle states
const loadToggleStates = (): CommentState => {
  if (typeof window !== 'undefined') {
    try {
      const stored = localStorage.getItem('commentToggleStates');
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (e) {
      // Log the error using a custom logging utility or handle it silently
      // Example: logError('Error loading comment toggle states:', e);
    }
  }
  return {};
};

const saveToggleState = (commentId: string, isOpen: boolean) => {
  if (typeof window !== 'undefined') {
    try {
      const currentStates = loadToggleStates();
      currentStates[commentId] = isOpen;
      localStorage.setItem('commentToggleStates', JSON.stringify(currentStates));
    } catch (e) {
      // Log the error using a custom logging utility or handle it silently
      // Example: logError('Error saving comment toggle state:', e);
    }
  }
};

// Define interface for reply structure to match comment.tsx
interface ReplyObject {
  id: string;
  value: string;
  created: Date;
  documents: {
    user: {
      name: string;
      id: string;
    };
  };
  projectId: string;
  parentComment?: string;
  childReplies: ReplyObject[];
}

interface ToggleRepliesProps {
  replies: ReplyObject[];
}

export function ToggleReplies({ replies }: ToggleRepliesProps) {
  // Use the first reply's parent as a unique ID for this toggle group
  // Make sure to handle the case where parentComment might be undefined or null
  const toggleGroupId = replies.length > 0 && replies[0].parentComment ? replies[0].parentComment : 'top';

  // Initialize from localStorage if available
  const [showReplies, setShowReplies] = useState<boolean>(false);

  // Load saved toggle state on mount
  useEffect(() => {
    // Check if we should restore toggle states
    const shouldPreserveToggles = typeof window !== 'undefined' && sessionStorage.getItem('preserveToggles') === 'true';

    // If we're coming back from a refresh with preserve flag
    if (shouldPreserveToggles) {
      const backup = sessionStorage.getItem('commentToggleStatesBackup');
      if (backup) {
        // Restore the backup
        localStorage.setItem('commentToggleStates', backup);
        // Clear backup and flag
        sessionStorage.removeItem('commentToggleStatesBackup');
        sessionStorage.removeItem('preserveToggles');
      }
    }

    // Now load the (possibly restored) state
    const states = loadToggleStates();
    // Make sure toggleGroupId is a string before using as key
    const toggleKey =
      typeof toggleGroupId === 'object' && toggleGroupId !== null
        ? String(toggleGroupId)
        : String(toggleGroupId || 'top');

    const savedState = states[toggleKey];
    if (savedState !== undefined) {
      setShowReplies(savedState);
    }
  }, [toggleGroupId]);

  // Save toggle state on change
  const handleToggleReplies = (isOpen: boolean) => {
    setShowReplies(isOpen);
    // Convert toggleGroupId to string safely
    const toggleKey =
      typeof toggleGroupId === 'object' && toggleGroupId !== null
        ? String(toggleGroupId)
        : String(toggleGroupId || 'top');
    saveToggleState(toggleKey, isOpen);
  };

  return (
    <div className="ml-8">
      {!showReplies ? (
        <button
          onClick={() => handleToggleReplies(true)}
          className="text-xs bg-blue-100 text-blue-700 hover:bg-blue-200 hover:text-blue-800 flex items-center gap-1 px-3 py-1.5 rounded mb-2 mt-1"
          data-toggle-group={String(toggleGroupId || 'top')}
        >
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M7 10L12 15L17 10"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <span>
            Show {replies.length} {replies.length === 1 ? 'reply' : 'replies'}
          </span>
        </button>
      ) : (
        <>
          <button
            onClick={() => handleToggleReplies(false)}
            className="text-xs bg-blue-100 text-blue-700 hover:bg-blue-200 hover:text-blue-800 flex items-center gap-1 px-3 py-1.5 rounded mb-2 mt-1"
            data-toggle-group={String(toggleGroupId || 'top')}
          >
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M17 14L12 9L7 14"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <span>Hide replies</span>
          </button>
          <div className="pl-4">
            {/* We'll use a different approach to render replies */}
            {showReplies &&
              replies.map((reply) => (
                <div key={reply.id} id={`comment-${reply.id}`} className="mt-2 mb-4">
                  <div className="font-bold">{reply.value}</div>
                  <div className="flex flex-wrap justify-between items-center gap-2 mt-2">
                    <div className="flex gap-3 text-xs">
                      <div>{reply.documents?.user?.name}</div>
                      <div>{new Date(reply.created).toLocaleString()}</div>
                    </div>
                    <div className="flex justify-end gap-2">
                      <ReplyForm projectId={reply.projectId || ''} parentCommentId={reply.id} />
                      <Form
                        action={async (prevState, formData) => {
                          try {
                            const response = await fetch('/api/comments/delete', {
                              method: 'POST',
                              body: formData,
                            });

                            const data = await response.json();

                            if (!response.ok) {
                              return { error: data.error || 'Failed to delete comment' };
                            }

                            // Just delete the comment DOM element to avoid full refresh
                            const commentElement = document.getElementById(`comment-${formData.get('comment_id')}`);
                            if (commentElement) {
                              commentElement.remove();
                            } else {
                              // If we can't find the element to remove, fall back to reload
                              setTimeout(() => {
                                window.location.reload();
                              }, 500);
                            }

                            return { error: null };
                          } catch (error) {
                            return { error: 'Failed to delete comment' };
                          }
                        }}
                      >
                        <input type="hidden" name="id" value={reply.projectId || ''} readOnly />
                        <input type="hidden" name="comment_id" value={reply.id} readOnly />
                        <DeleteButton />
                      </Form>
                    </div>
                  </div>

                  {/* Nested replies are rendered here by recursively using ToggleReplies */}
                  {reply.childReplies && reply.childReplies.length > 0 && (
                    <div className="mt-4">
                      <ToggleReplies replies={reply.childReplies} />
                    </div>
                  )}
                </div>
              ))}
          </div>
        </>
      )}
    </div>
  );
}
