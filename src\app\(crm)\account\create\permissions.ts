import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function () {
  'use server';
  const { user } = await validateRequest();
  if (!user) throw new ServerError('Forbidden: you can not create accounts.');

  const canCreate = await can(user, 'create_account');
  if (!canCreate) throw new ServerError('Forbidden: you can not create accounts.');

  return { canCreate, user };
});
