/* eslint-disable @next/next/no-page-custom-font */
import { Download } from 'lucide-react';
import Link from 'next/link';
import { Metadata, ResolvingMetadata } from 'next/types';

import { Button, Form, FormButton } from '@/lib/form';
import { formatDate } from '@/lib/formatting';
import { ActionFooter } from '@/lib/nav';
import { Widget } from '@/lib/widget';
import { SIGNATURE_TYPE } from '@/schemas/projects/subdocuments';

import { sign } from './actions';
import { getData, getSignatureName } from './helpers';
import { InlineProps, ProjectFileViewProps, SignatureBlockProps } from './types';

export async function generateMetadata(props: ProjectFileViewProps, parent: ResolvingMetadata): Promise<Metadata> {
  const {
    params: { id: projectId, fileid: fileId },
  } = props;

  const parentMetadata = await parent;
  const { fileDoc } = await getData(projectId, fileId);

  return {
    title: [fileDoc.filename, parentMetadata.title?.absolute].join(' | '),
  };
}

export default async function ProjectFilePage(props: Readonly<ProjectFileViewProps>) {
  const {
    params: { id: projectId, fileid: fileId },
  } = props;

  const { fileDoc } = await getData(projectId, fileId);

  return (
    <div className="flex flex-col gap-6">
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" />
      <link href="https://fonts.googleapis.com/css2?family=Cedarville+Cursive&display=swap" rel="stylesheet" />

      <Widget
        label={`File: ${fileDoc.filename}`}
        action={
          <Link href={`/file/${fileDoc.id}`} target="_blank">
            <Download />
          </Link>
        }
      >
        <Inline file={fileDoc} />
      </Widget>

      <ActionFooter left={<Link href={`/project/${projectId}`}>Back to Project</Link>} />
    </div>
  );
}

function Inline(props: Readonly<InlineProps>) {
  const mimetype = props.file.mimetype;

  if (mimetype.startsWith('image/')) {
    return (
      <Link href={`/file/${props.file.id}`} target="_blank">
        <img src={`/file/${props.file.id}`} alt={props.file.filename} className="block object-contain max-h-96" />
      </Link>
    );
  }

  if (mimetype.startsWith('video/')) {
    return (
      <video controls>
        <source src={`/file/${props.file.id}`} type={mimetype} className="block object-contain aspect-video" />
      </video>
    );
  }

  if (mimetype.includes('pdf')) {
    return (
      <object type={mimetype} data={`/file/${props.file.id}#view=FitV`} className="w-3/4 mx-auto aspect-letter">
        <Button href={`/file/${props.file.id}`}>Download Your PDF</Button>
      </object>
    );
  }

  return <Button href={`/file/${props.file.id}`}>Download</Button>;
}

//! Not in use right now but could be used in the future
// eslint-disable-next-line @typescript-eslint/no-unused-vars
async function SignatureBlock(props: Readonly<SignatureBlockProps>) {
  const { canSignAsAdmin, canSignWithCustomerInperson, canSignAsPm, file } = await getData(
    props.projectId,
    props.fileId,
  );
  const signatures = file.signatures;

  const existingSignature = signatures.find((sign) => sign.type === props.type);
  const isSigned = !!existingSignature;
  const name = await getSignatureName(props.type, props.projectId, props.fileId);
  const action = sign.bind(null, props.type, props.projectId, props.fileId);

  let canSign = true;

  if (props.type === SIGNATURE_TYPE.CUSTOMER && !canSignWithCustomerInperson) {
    canSign = false;
  } else if (props.type === SIGNATURE_TYPE.ADMIN && !canSignAsAdmin) {
    canSign = false;
  } else if (props.type === SIGNATURE_TYPE.PM && !canSignAsPm) {
    canSign = false;
  }

  let label = '';
  if (props.type === SIGNATURE_TYPE.CUSTOMER) label = 'Customer';
  if (props.type === SIGNATURE_TYPE.ADMIN) label = 'Admin Approval';
  if (props.type === SIGNATURE_TYPE.PM) label = 'Project Manager Approval';

  const signatureContainerClassList = [
    'lg:whitespace-nowrap',
    'lg:flex',
    'gap-4',
    'items-center',
    'rounded',
    'px-6',
    'py-3',
    'text-lg',
    'font-bold',
  ];

  if (isSigned) {
    signatureContainerClassList.push('bg-green-700', 'text-white');
  } else {
    signatureContainerClassList.push('bg-white', 'text-black');
  }

  return (
    <Form className="select-none grid grid-cols-4 gap-4 items-center" action={action}>
      <div className="col-span-3">
        <div className={signatureContainerClassList.join(' ')}>
          <div>{label}:</div>

          {isSigned ? (
            <div className="border-b-2 border-black w-full lg:flex gap-4 items-center">
              <div className="font-cursive w-full">{name}</div>
              <div>Date:</div>
              <div>{formatDate(existingSignature.created, true)}</div>
            </div>
          ) : (
            <div className="w-full border-b-2 border-black">&nbsp;</div>
          )}
        </div>
      </div>
      <div>
        <FormButton disabled={isSigned || !canSign}>Sign</FormButton>
      </div>
    </Form>
  );
}
