import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { ServerError } from '@/server';
import { getAccount } from '@/server/accounts';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function () {
  'use server';
  const { user, account } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not create locations.');

  const { googleKeyIsSet, googleApiKey } = await getAccount(account._id);

  const canCreate = await can(user, 'create_location');
  if (!canCreate) throw new ServerError('Forbidden: you can not create locations.');

  return { canCreate, user, account, canInputCoords: !googleKeyIsSet, googleApiKey };
});
