import type { Metadata, ResolvingMetadata } from 'next';
import Link from 'next/link';

import { Field, Form, FormButton } from '@/lib/form';
import { ActionFooter } from '@/lib/nav';
import { Widget } from '@/lib/widget';
import PlaceAutocomplete from '@/lib/address/autocomplete';

import { create } from './actions';
import { validatePermissions } from './permissions';

export async function generateMetadata(_: unknown, parent: ResolvingMetadata): Promise<Metadata> {
  const parentMetadata = await parent;

  return {
    title: 'Create | ' + parentMetadata.title?.absolute,
  };
}

export default async function LocationCreate() {
  const { canInputCoords, googleApiKey, account } = await validatePermissions();

  return (
    <div>
      <Form className="space-y-6" action={create}>
        <Widget label="New Location" className="h-96">
          <div className="grid grid-cols-1 gap-4">
            <Field name="name" label="Name" required />
            <PlaceAutocomplete apiKey={googleApiKey} name="address" disabled={canInputCoords} />
            {/* <AddressFields name="address" required editCoord={canInputCoords} /> */}
          </div>
        </Widget>

        <ActionFooter
          left={<Link href={`/account/${account._id.toString()}/custom-fields`}>Back to Locations</Link>}
          right={<FormButton>Save</FormButton>}
        />
      </Form>
    </div>
  );
}
