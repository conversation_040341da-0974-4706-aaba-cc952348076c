import Link from 'next/link';

import { Action, Field, Form, FormButton } from '@/lib/form';
import { ActionFooter } from '@/lib/nav';
import { Widget } from '@/lib/widget';

import { getData } from './helpers';

interface EditDeadLeadReasonPageProps {
  params: { id: string };
}

export default async function EditDeadLeadReasonPage({ params }: EditDeadLeadReasonPageProps) {
  const { deadLeadReason, editAction, canDelete, account } = await getData(params.id);

  return (
    <Form className="flex flex-col gap-6" action={editAction}>
      <Widget label={deadLeadReason.name || 'Dead Lead Reason'} className="flex flex-col gap-4">
        <Field label="Name" name="name" value={deadLeadReason.name} required />
      </Widget>

      <ActionFooter
        left={<Link href={`/account/${account._id.toString()}/custom-fields`}>Back to Dead Lead Reasons</Link>}
        right={
          <>
            <FormButton>Save</FormButton>
            {!!canDelete && <Action />}
          </>
        }
      />
    </Form>
  );
}
