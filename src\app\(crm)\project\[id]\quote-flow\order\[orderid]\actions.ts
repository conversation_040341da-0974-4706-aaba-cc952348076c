import { createHash } from 'node:crypto';

import { generateId } from 'lucia';
import mongoose from 'mongoose';
import { revalidatePath } from 'next/cache';
import { headers } from 'next/headers';

import { db } from '@/lib';
import { getNotificationModel, getProjectModel, NOTIFTYPE } from '@/schemas';
import { getProjectSignatureModel, SIGNATURE_TYPE } from '@/schemas/projects/subdocuments';
import { ActionResult } from '@/lib/form';
import { sendEmail } from '@/lib/mail';

import { getData, getSignatureName } from './helpers';

export async function sign(type: SIGNATURE_TYPE, projectId: string, orderId: string): Promise<ActionResult> {
  'use server';

  try {
    await db();

    const projectModel = await getProjectModel();
    const project = await projectModel
      .findOne({ _id: new mongoose.mongo.ObjectId(projectId) })
      .populate([
        'documents.assignedTo',
        'documents.account',
        'documents.customer',
        { path: 'files', populate: 'documents.file' },
      ]);
    if (!project) throw new Error('Not found: project is not found');

    const order = project.orders.find((order) => order._id.toString() === orderId);
    if (!order) throw new Error('Not found: order not found');

    const existingSignature = order.signatures.find((s) => s.type === type);
    if (existingSignature) throw new Error('Error: this signature was already captured');

    const signatureModel = await getProjectSignatureModel();
    const notificationModel = await getNotificationModel();

    const name = await getSignatureName(type, projectId, orderId);
    const readonlyHeaders = await headers();
    const ipAddress = readonlyHeaders.get('x-forwarded-for')?.split(',')[0];

    const data = JSON.stringify({
      projectId: project._id,
      ipAddress: ipAddress === '::1' ? 'localhost' : ipAddress,
      datetime: new Date(),
      customerEmail: project.documents?.customer?.email,
    });

    const hash = createHash('sha256').update(data).digest('hex');

    let createdById: mongoose.Types.ObjectId | undefined;

    if (type === SIGNATURE_TYPE.CUSTOMER) {
      createdById = project.documents?.customer?._id;
    }

    if ([SIGNATURE_TYPE.ADMIN, SIGNATURE_TYPE.PM].includes(type)) {
      const { user } = await getData(projectId, orderId);

      createdById = user._id;
    }

    const signature = new signatureModel({
      type,
      name,
      hash,
      ipAddress,
      createdBy: createdById,
    });

    order.signatures.push(signature);

    await project.save({ timestamps: false });

    await notificationModel.create({
      account: project.account,
      user: project.documents?.assignedTo ?? createdById,
      link: `/project/${project._id.toString()}/quote-flow/order/${order._id.toString()}`,
      message: `Trussi.AI ${project?.documents?.account?.name} Order signed`,
      type: NOTIFTYPE.PROJECT,
    });
  } catch (error: unknown) {
    if (error instanceof Error) return { error: error.message };

    return { error: 'Unknown error' };
  }

  revalidatePath(`/project/${projectId}/order/${orderId}/sign`);
  return { error: null };
}

export async function sendSignEmailToCustomer(
  projectId: string,
  orderId: string,
  _: unknown,
  formData: FormData,
): Promise<ActionResult> {
  'use server';
  const { account, project } = await getData(projectId, orderId);

  if (!project) throw new Error('Not Found: project is unavailable.');

  const customerEmail = project?.documents?.customer?.email;
  if (!customerEmail) {
    throw new Error('Not Found: customer not found in project.');
  }

  const origin = formData.get('_origin');

  const key = generateId(15);
  const url = new URL(origin + `/project/${projectId}/order/${orderId}/sign?key=${key}`);

  const idx = project.orders.findIndex((order) => order._id.toString() === orderId);

  project.orders[idx].signKey = key;

  await project.save({ timestamps: false });

  await sendEmail({
    from: '"Trussi.AI" <<EMAIL>>',
    to: customerEmail,
    subject: `Trussi.AI Sign your ${account.name} Order`,
    text: 'Please click the following link to sign your Order:\n\n' + url.toString(),
  });

  return {
    message: 'Signature request sent successfully.',
    error: null,
  };
}
