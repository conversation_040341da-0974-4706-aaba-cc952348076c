import Link from 'next/link';

import { formatPhoneNumber, formatDate } from '@/lib/formatting';
import { DisplayData } from '@/lib/view';
import { EditIcon, Widget } from '@/lib/widget';
import { ActionFooter } from '@/lib/nav';
import { PROJECTMILESTONE } from '@/schemas/projects/subdocuments/milestone/enums';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { getData } from './helpers';

export default async function ViewCustomer({ params }: Readonly<{ params: { id: string } }>) {
  const { customer, canEdit, projects, canView } = await getData(params.id);
  const canViewLocations =
    typeof canView === 'object' && (canView as { permissions?: string[] }).permissions?.includes('location.view');

  return (
    <div className="space-y-6">
      <Widget label={customer.name} action={canEdit && <EditIcon href={`/customer/${customer.id}/edit`} />}>
        <DisplayData label="First Name" value={customer.firstName} />
        <DisplayData label="Last Name" value={customer.lastName} />
        <DisplayData label="Phone" value={formatPhoneNumber(customer.phone)} />
        <DisplayData label="Email" value={customer.email} />
      </Widget>

      {projects && projects.length > 0 && (
        <Widget label="Projects">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Assigned To</TableHead>
                <TableHead>Created</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {projects.map((project) => (
                <TableRow key={project._id.toString()}>
                  <TableCell>
                    <div className="font-medium">
                      <Link href={`/project/${project._id}`} className="hover:no-underline underline">
                        {project.name}
                      </Link>
                    </div>
                    <div className="text-xs font-light">{customer.name}</div>
                  </TableCell>
                  <TableCell>
                    {(() => {
                      // Get current milestone (last one in the array, or default to LEAD)
                      const milestones = project.milestones || [];
                      const currentMilestone =
                        milestones.length > 0 ? milestones[milestones.length - 1].name : PROJECTMILESTONE.LEAD;

                      const isDead = project.isDead || false;
                      const isCanTableCelled = project.isCancelled || false;

                      // Set proper styling based on status
                      const className = isDead || isCanTableCelled ? 'text-red-500' : '';

                      return <span className={className}>{currentMilestone}</span>;
                    })()}
                  </TableCell>
                  <TableCell>
                    {project.documents?.location ? (
                      canViewLocations ? (
                        <Link
                          href={`/location/${project.documents.location._id}`}
                          className="hover:no-underline underline"
                        >
                          {project.documents.location.name}
                        </Link>
                      ) : (
                        project.documents.location.name
                      )
                    ) : (
                      'N/A'
                    )}
                  </TableCell>
                  <TableCell>{project.documents?.assignedTo?.name || 'Unassigned'}</TableCell>
                  <TableCell>{project.created ? formatDate(project.created) : 'Unknown'}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Widget>
      )}

      <ActionFooter left={<Link href="/customer">Back to Customers</Link>} />
    </div>
  );
}
