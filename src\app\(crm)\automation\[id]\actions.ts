'use server';

import { redirect } from 'next/navigation';
import mongoose from 'mongoose';

import { getAutomationModel } from '@/schemas';
import { ActionResult } from '@/lib/form/types';
import { AUTOMATION_ACTION_TYPE, AUTOMATION_TRIGGER_TYPE, PROJECT_STATUS } from '@/schemas/automations/types';

import { getData } from './helpers';

export async function edit(id: string, _: unknown, formData: FormData): Promise<ActionResult> {
  'use server';
  try {
    const { automation, user, canEdit } = await getData(id);

    if (!canEdit) {
      return { error: 'You do not have permission to edit this automation' };
    }

    const name = formData.get('name') as string;
    const description = formData.get('description') as string;
    const triggerType = formData.get('triggerType') as string;
    const actionType = formData.get('actionType') as string;
    const isActive = formData.get('isActive') === 'on';

    if (!name) {
      return { error: 'Name is required' };
    }

    if (!triggerType) {
      return { error: 'Trigger type is required' };
    }

    if (!actionType) {
      return { error: 'Action type is required' };
    }

    // Update basic automation properties
    automation.name = name;
    automation.description = description;
    automation.isActive = isActive;
    automation.trigger.type = triggerType as AUTOMATION_TRIGGER_TYPE;
    automation.actions[0].type = actionType as AUTOMATION_ACTION_TYPE;
    automation.modifiedBy = user._id;

    // Handle project status change trigger conditions
    if (triggerType === 'project_status_changed') {
      const fromStatus = formData.get('fromStatus') as string;
      const toStatus = formData.get('toStatus') as string;
      const daysOfInactivity = parseInt((formData.get('daysOfInactivity') as string) || '0', 10);

      automation.trigger.projectStatusChangeConditions = {
        fromStatus: fromStatus as PROJECT_STATUS,
        toStatus: toStatus as PROJECT_STATUS,
        daysOfInactivity,
      };
    } else {
      // Clear project status change conditions if trigger type is different
      automation.trigger.projectStatusChangeConditions = undefined;
    }

    // Handle email action configuration
    if (actionType === 'send_email') {
      const emailSubject = formData.get('emailSubject') as string;
      const emailBody = formData.get('emailBody') as string;
      const emailRecipientType = formData.get('emailRecipientType') as string;
      const customEmails = formData.get('customEmails') as string;

      const recipients = [];
      if (emailRecipientType === 'custom' && customEmails) {
        const emails = customEmails.split(',').map((email) => email.trim());
        for (const email of emails) {
          if (email) {
            recipients.push({
              type: 'custom' as const,
              email,
            });
          }
        }
      }

      automation.actions[0].emailConfig = {
        subject: emailSubject,
        body: emailBody,
        recipients,
      };

      // Clear SMS config if action type is email
      automation.actions[0].smsConfig = undefined;
    }

    // Handle SMS action configuration
    if (actionType === 'send_sms') {
      const smsMessage = formData.get('smsMessage') as string;
      const smsRecipientType = formData.get('smsRecipientType') as string;
      const customPhones = formData.get('customPhones') as string;

      const recipients = [];
      if (smsRecipientType === 'custom' && customPhones) {
        const phones = customPhones.split(',').map((phone) => phone.trim());
        for (const phoneNumber of phones) {
          if (phoneNumber) {
            recipients.push({
              type: 'custom' as const,
              phoneNumber,
            });
          }
        }
      }

      automation.actions[0].smsConfig = {
        message: smsMessage,
        recipients,
      };

      // Clear email config if action type is SMS
      automation.actions[0].emailConfig = undefined;
    }

    await automation.save();

    return { error: null, message: 'Automation updated successfully' };
  } catch (error) {
    if (error instanceof Error) {
      return { error: error.message };
    }
    return { error: 'An unknown error occurred' };
  }
}

export async function remove(id: string): Promise<ActionResult> {
  'use server';
  try {
    const { canDelete } = await getData(id);

    if (!canDelete) {
      return { error: 'You do not have permission to delete this automation' };
    }

    const automationModel = await getAutomationModel();
    await automationModel.deleteOne({ _id: new mongoose.Types.ObjectId(id) });
  } catch (error) {
    if (error instanceof Error) {
      return { error: error.message };
    }
    return { error: 'An unknown error occurred' };
  }

  // Redirect outside of try-catch to avoid NEXT_REDIRECT error
  redirect('/automation');
}
