import Link from 'next/link';

import { Action, Field, Form, FormButton } from '@/lib/form';
import { ActionFooter } from '@/lib/nav';
import { Widget } from '@/lib/widget';

import { getData } from './helpers';

interface EditLeadSourcePageProps {
  params: { id: string };
}

export default async function EditLeadSourcePage({ params }: EditLeadSourcePageProps) {
  const { leadSource, editAction, canDelete, account } = await getData(params.id);

  return (
    <Form className="flex flex-col gap-6" action={editAction}>
      <Widget label={leadSource.name || 'Lead Source'} className="flex flex-col gap-4">
        <Field label="Name" name="name" value={leadSource.name} required />
      </Widget>

      <ActionFooter
        left={<Link href={`/account/${account._id.toString()}/custom-fields`}>Back to Lead Sources</Link>}
        right={
          <>
            <FormButton>Save</FormButton>
            {!!canDelete && <Action />}
          </>
        }
      />
    </Form>
  );
}
