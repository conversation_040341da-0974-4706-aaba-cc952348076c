import { cache } from 'react';

import { getAutomationModel } from '@/schemas';

import { validatePermissions } from './permissions';

export const getData = cache(async function () {
  'use server';
  const permissions = await validatePermissions();
  const { account } = permissions;

  const automationModel = await getAutomationModel();
  const automations = await automationModel.find({ account: account._id }).sort({ name: 1 });

  return { ...permissions, automations };
});
