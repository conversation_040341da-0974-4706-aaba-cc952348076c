'use server';

import mongoose from 'mongoose';
import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

import { db } from '@/lib';
import { ActionResult } from '@/lib/form';
import { getDeadLeadReasonModel } from '@/schemas';

export async function edit(id: string, canDelete: boolean, _: unknown, formData: FormData): Promise<ActionResult> {
  'use server';
  const { _action } = Object.fromEntries(formData);

  if (_action === 'delete') {
    if (!canDelete) return { error: 'Forbidden: you do not have permissions to delete this dead lead reason.' };

    try {
      await db();
      const deadLeadReasonModel = await getDeadLeadReasonModel();
      await deadLeadReasonModel.deleteOne({ _id: new mongoose.mongo.ObjectId(id) });
    } catch (e) {
      if (e instanceof Error) return { error: e.message };

      return { error: 'Unknown error' };
    }

    redirect(`/dead-lead-reason`);
  }

  try {
    await db();
    const deadLeadReasonModel = await getDeadLeadReasonModel();

    const { name } = Object.fromEntries(formData);
    const deadLeadReason = await deadLeadReasonModel.findById(id);
    if (!deadLeadReason) return { error: 'Dead Lead Reason not found' };

    deadLeadReason.set('name', name);

    await deadLeadReason.save();
  } catch (e) {
    if (e instanceof Error) return { error: e.message };

    return { error: 'Unknown error' };
  }

  revalidatePath(`/dead-lead-reason/${id}`);
  redirect(`/dead-lead-reason`);
}
