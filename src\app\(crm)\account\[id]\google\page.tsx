import { Metadata, ResolvingMetadata } from 'next';

import { WidgetEdit } from '@/lib/account';
import { Field, Form, SecretField } from '@/lib/form';

import { getData } from './helpers';
import { Props } from './types';

export async function generateMetadata(_: unknown, parent: ResolvingMetadata): Promise<Metadata> {
  const parentMetadata = await parent;

  return {
    title: 'Google API | ' + parentMetadata.title?.absolute,
  };
}

export default async function EditPage({ params }: Readonly<Props>) {
  const { account, googleKeyIsSet, editAction } = await getData(params.id);

  return (
    <Form action={editAction}>
      <WidgetEdit label={account.name} id={account.id}>
        <div className="grid grid-cols-1 gap-4">
          <SecretField label="Edit API Key" isSet={googleKeyIsSet}>
            <Field autoComplete="off" name="apikey" label="API Key" type="password" />
            <p className="text-xs text-gray-200">Services: places-backend.googleapis.com</p>
          </SecretField>
        </div>
      </WidgetEdit>
    </Form>
  );
}
