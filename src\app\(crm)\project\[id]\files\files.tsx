import Link from 'next/link';

import { Field, Form, FormButton } from '@/lib/form';
import { assertProject } from '@/lib/project';
import { assert } from '@/lib/typescript';
import { ListItem, NotFound, Widget } from '@/lib/widget';
import type { ProjectDoc } from '@/schemas';
import { FILE_FOLDER } from '@/schemas/projects/subdocuments/file/enums';
import type { ProjectFile } from '@/schemas/projects/subdocuments';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { deleteAction, moveFile, renameFile, uploadFile } from './actions';
import { DeleteButton } from './delete-button';
import { getData } from './helpers';
import { MoveButton } from './move-button';
import { RenameButton } from './rename-button';
import { FileProps, FilesProps } from './types';

// Custom styled select component to ensure proper visibility
function CategorySelect({
  name,
  defaultValue,
  children,
}: {
  name: string;
  defaultValue: string;
  children: React.ReactNode;
}) {
  return (
    <div className="space-y-1">
      <label className="block text-xs text-gray-500 font-medium select-none">Category</label>
      <div className="text-gray-400">
        <Select
          name={name}
          defaultValue={defaultValue}
          // className="block w-full rounded-md border-0 p-1.5 shadow-sm bg-slate-800 text-white"
          // style={{
          //   backgroundColor: '#1e293b',
          //   color: 'white',
          // }}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select" />
          </SelectTrigger>
          <SelectContent>{children}</SelectContent>
        </Select>
      </div>
    </div>
  );
}

export async function Files(props: Readonly<FilesProps>) {
  let project: ProjectDoc | null = null;
  let canCreateFiles = false;
  let canDeleteFiles = false;
  let canEditFiles = false;
  let canList = false;
  let canView = false;

  try {
    ({ canCreateFiles, project, canDeleteFiles, canEditFiles, canList, canView } = await getData(props.id));
  } catch {}

  if (!canList) return null;

  assert<ProjectDoc>(project, !!project);

  const uploadFileAction = uploadFile.bind(null, props.id);

  // Organize files by folder categories
  const filesByCategory = new Map<string, Array<ProjectFile>>();

  // Initialize document categories
  filesByCategory.set('job_paperwork', []);
  filesByCategory.set('email_documents', []);
  filesByCategory.set('permits', []);
  filesByCategory.set('roof_report', []);
  filesByCategory.set('labor_invoices', []);
  filesByCategory.set('material_invoices', []);
  filesByCategory.set('other_documents', []);

  // Initialize photo categories
  filesByCategory.set('photos', []); // For legacy support
  filesByCategory.set('photos_jobsite_before', []);
  filesByCategory.set('photos_jobsite_after', []);
  filesByCategory.set('photos_material_check', []);
  filesByCategory.set('photos_damage', []);
  filesByCategory.set('photos_supplement', []);
  filesByCategory.set('photos_reinspect', []);
  filesByCategory.set('photos_other', []);

  // Sort files into categories
  project.files.forEach((file) => {
    const isPhotoOrVideo =
      file.documents?.file?.mimetype?.startsWith('image/') || file.documents?.file?.mimetype?.startsWith('video/');
    const folder = file.folder || '/';

    if (isPhotoOrVideo) {
      // Sort into specific photo categories based on folder
      if (folder.startsWith('/photos/jobsite-before/')) {
        filesByCategory.get('photos_jobsite_before')?.push(file);
      } else if (folder.startsWith('/photos/jobsite-after/')) {
        filesByCategory.get('photos_jobsite_after')?.push(file);
      } else if (folder.startsWith('/photos/material-check/')) {
        filesByCategory.get('photos_material_check')?.push(file);
      } else if (folder.startsWith('/photos/materials/')) {
        // For backward compatibility
        filesByCategory.get('photos_material_check')?.push(file);
      } else if (folder.startsWith('/photos/damage/')) {
        filesByCategory.get('photos_damage')?.push(file);
      } else if (folder.startsWith('/photos/supplement/')) {
        filesByCategory.get('photos_supplement')?.push(file);
      } else if (folder.startsWith('/photos/reinspect/')) {
        filesByCategory.get('photos_reinspect')?.push(file);
      } else if (folder.startsWith('/photos/other/')) {
        filesByCategory.get('photos_other')?.push(file);
      } else {
        // Fallback for photos without specific category (legacy support)
        filesByCategory.get('photos')?.push(file);
      }
    } else {
      if (folder.startsWith('/documents/jobpaperwork/')) {
        filesByCategory.get('job_paperwork')?.push(file);
      } else if (folder.startsWith('/documents/emaildocuments/')) {
        filesByCategory.get('email_documents')?.push(file);
      } else if (folder.startsWith('/documents/permits/')) {
        filesByCategory.get('permits')?.push(file);
      } else if (folder.startsWith('/documents/roofreport/')) {
        filesByCategory.get('roof_report')?.push(file);
      } else if (folder.startsWith('/documents/laborinvoices/')) {
        filesByCategory.get('labor_invoices')?.push(file);
      } else if (folder.startsWith('/documents/materialinvoices/')) {
        filesByCategory.get('material_invoices')?.push(file);
      } else {
        filesByCategory.get('other_documents')?.push(file);
      }
    }
  });

  // Get all photos/videos and documents for legacy support
  const photos = project.files.filter(
    (file) =>
      file.folder?.startsWith('/photos') ||
      (file.documents?.file?.mimetype &&
        (file.documents.file.mimetype.startsWith('image/') || file.documents.file.mimetype.startsWith('video/'))),
  );

  const documents = project.files.filter(
    (file) =>
      file.folder?.startsWith('/documents') ||
      (file.documents?.file?.mimetype &&
        !file.documents.file.mimetype.startsWith('image/') &&
        !file.documents.file.mimetype.startsWith('video/')),
  );

  return (
    <>
      <Widget
        label="Documents"
        action={
          canCreateFiles && (
            <Form action={uploadFileAction}>
              <div className="flex flex-col space-y-2 md:flex-row md:items-end md:space-y-0 md:space-x-2">
                <div>
                  <Field type="file" name="file" label="Select document" multiple={true} />
                </div>
                <div>
                  <CategorySelect name="folder" defaultValue="">
                    {/* <SelectItem value="">Select Category</SelectItem> */}
                    <SelectItem value={FILE_FOLDER.JOB_PAPERWORK}>Job Paperwork</SelectItem>
                    <SelectItem value={FILE_FOLDER.EMAIL_DOCUMENTS}>Email Documents</SelectItem>
                    <SelectItem value={FILE_FOLDER.PERMITS}>Permits</SelectItem>
                    <SelectItem value={FILE_FOLDER.ROOF_REPORT}>Roof Report</SelectItem>
                    <SelectItem value={FILE_FOLDER.LABOR_INVOICES}>Labor Invoices</SelectItem>
                    <SelectItem value={FILE_FOLDER.MATERIAL_INVOICES}>Material Invoices</SelectItem>
                  </CategorySelect>
                </div>
                <div>
                  <input type="hidden" name="_id" value={props.id} readOnly />
                  <FormButton>Upload</FormButton>
                </div>
              </div>
            </Form>
          )
        }
      >
        {/* Job Paperwork */}
        {filesByCategory.get('job_paperwork')?.length ? (
          <>
            <h3 className="font-semibold text-md mt-4 mb-2">Job Paperwork</h3>
            {filesByCategory
              .get('job_paperwork')
              ?.map((file) => (
                <File
                  canView={canView}
                  key={file.file.toString()}
                  {...{ file }}
                  canDelete={canDeleteFiles}
                  canEdit={canEditFiles}
                />
              ))}
          </>
        ) : null}

        {/* Email Documents */}
        {filesByCategory.get('email_documents')?.length ? (
          <>
            <h3 className="font-semibold text-md mt-4 mb-2">Email Documents</h3>
            {filesByCategory
              .get('email_documents')
              ?.map((file) => (
                <File
                  canView={canView}
                  key={file.file.toString()}
                  {...{ file }}
                  canDelete={canDeleteFiles}
                  canEdit={canEditFiles}
                />
              ))}
          </>
        ) : null}

        {/* Permit Documents */}
        {filesByCategory.get('permits')?.length ? (
          <>
            <h3 className="font-semibold text-md mt-4 mb-2">Permits</h3>
            {filesByCategory
              .get('permits')
              ?.map((file) => (
                <File
                  canView={canView}
                  key={file.file.toString()}
                  {...{ file }}
                  canDelete={canDeleteFiles}
                  canEdit={canEditFiles}
                />
              ))}
          </>
        ) : null}

        {/* Roof Report */}
        {filesByCategory.get('roof_report')?.length ? (
          <>
            <h3 className="font-semibold text-md mt-4 mb-2">Roof Report</h3>
            {filesByCategory
              .get('roof_report')
              ?.map((file) => (
                <File
                  canView={canView}
                  key={file.file.toString()}
                  {...{ file }}
                  canDelete={canDeleteFiles}
                  canEdit={canEditFiles}
                />
              ))}
          </>
        ) : null}

        {/* Labor Invoices */}
        {filesByCategory.get('labor_invoices')?.length ? (
          <>
            <h3 className="font-semibold text-md mt-4 mb-2">Labor Invoices</h3>
            {filesByCategory
              .get('labor_invoices')
              ?.map((file) => (
                <File
                  canView={canView}
                  key={file.file.toString()}
                  {...{ file }}
                  canDelete={canDeleteFiles}
                  canEdit={canEditFiles}
                />
              ))}
          </>
        ) : null}

        {/* Material Invoices */}
        {filesByCategory.get('material_invoices')?.length ? (
          <>
            <h3 className="font-semibold text-md mt-4 mb-2">Material Invoices</h3>
            {filesByCategory
              .get('material_invoices')
              ?.map((file) => (
                <File
                  canView={canView}
                  key={file.file.toString()}
                  {...{ file }}
                  canDelete={canDeleteFiles}
                  canEdit={canEditFiles}
                />
              ))}
          </>
        ) : null}

        {/* Other Documents */}
        {filesByCategory.get('other_documents')?.length ? (
          <>
            <h3 className="font-semibold text-md mt-4 mb-2">Other Documents</h3>
            {filesByCategory
              .get('other_documents')
              ?.map((file) => (
                <File
                  canView={canView}
                  key={file.file.toString()}
                  {...{ file }}
                  canDelete={canDeleteFiles}
                  canEdit={canEditFiles}
                />
              ))}
          </>
        ) : null}

        {documents.length === 0 && <NotFound>No documents available</NotFound>}
      </Widget>
      <div className="h-8" />
      <Widget
        label="Photos & Videos"
        action={
          canCreateFiles && (
            <Form action={uploadFileAction}>
              <div className="flex flex-col space-y-2 md:flex-row md:items-end md:space-y-0 md:space-x-2">
                <div>
                  <Field
                    type="file"
                    name="file"
                    label="Select photos/videos"
                    accept="image/*,video/*"
                    multiple={true}
                  />
                </div>
                <div>
                  <CategorySelect name="folder" defaultValue="">
                    <SelectItem value={FILE_FOLDER.PHOTOS_JOBSITE_BEFORE}>Job Site - Before</SelectItem>
                    <SelectItem value={FILE_FOLDER.PHOTOS_JOBSITE_AFTER}>Job Site - After</SelectItem>
                    <SelectItem value={FILE_FOLDER.PHOTOS_MATERIAL_CHECK}>Material Check</SelectItem>
                    <SelectItem value={FILE_FOLDER.PHOTOS_DAMAGE}>Damage</SelectItem>
                    <SelectItem value={FILE_FOLDER.PHOTOS_SUPPLEMENT}>Supplement</SelectItem>
                    <SelectItem value={FILE_FOLDER.PHOTOS_REINSPECT}>Reinspect</SelectItem>
                  </CategorySelect>
                </div>
                <div>
                  <input type="hidden" name="_id" value={props.id} readOnly />
                  <FormButton>Upload</FormButton>
                </div>
              </div>
            </Form>
          )
        }
      >
        {/* All Photos & Videos that don't have a specific category */}
        {filesByCategory.get('photos')?.length ? (
          <>
            <h3 className="font-semibold text-md mt-4 mb-2">All Photos & Videos</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {filesByCategory
                .get('photos')
                ?.map((file) => (
                  <File
                    canView={canView}
                    key={file.file.toString()}
                    {...{ file }}
                    canDelete={canDeleteFiles}
                    canEdit={canEditFiles}
                  />
                ))}
            </div>
          </>
        ) : null}

        {/* Job Site - Before */}
        {filesByCategory.get('photos_jobsite_before')?.length ? (
          <>
            <h3 className="font-semibold text-md mt-4 mb-2">Job Site - Before</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {filesByCategory
                .get('photos_jobsite_before')
                ?.map((file) => (
                  <File
                    canView={canView}
                    key={file.file.toString()}
                    {...{ file }}
                    canDelete={canDeleteFiles}
                    canEdit={canEditFiles}
                  />
                ))}
            </div>
          </>
        ) : null}

        {/* Job Site - After */}
        {filesByCategory.get('photos_jobsite_after')?.length ? (
          <>
            <h3 className="font-semibold text-md mt-4 mb-2">Job Site - After</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {filesByCategory
                .get('photos_jobsite_after')
                ?.map((file) => (
                  <File
                    canView={canView}
                    key={file.file.toString()}
                    {...{ file }}
                    canDelete={canDeleteFiles}
                    canEdit={canEditFiles}
                  />
                ))}
            </div>
          </>
        ) : null}

        {/* Material Check */}
        {filesByCategory.get('photos_material_check')?.length ? (
          <>
            <h3 className="font-semibold text-md mt-4 mb-2">Material Check</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {filesByCategory
                .get('photos_material_check')
                ?.map((file) => (
                  <File
                    canView={canView}
                    key={file.file.toString()}
                    {...{ file }}
                    canDelete={canDeleteFiles}
                    canEdit={canEditFiles}
                  />
                ))}
            </div>
          </>
        ) : null}

        {/* Damage */}
        {filesByCategory.get('photos_damage')?.length ? (
          <>
            <h3 className="font-semibold text-md mt-4 mb-2">Damage</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {filesByCategory
                .get('photos_damage')
                ?.map((file) => (
                  <File
                    canView={canView}
                    key={file.file.toString()}
                    {...{ file }}
                    canDelete={canDeleteFiles}
                    canEdit={canEditFiles}
                  />
                ))}
            </div>
          </>
        ) : null}

        {/* Supplement */}
        {filesByCategory.get('photos_supplement')?.length ? (
          <>
            <h3 className="font-semibold text-md mt-4 mb-2">Supplement</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {filesByCategory
                .get('photos_supplement')
                ?.map((file) => (
                  <File
                    canView={canView}
                    key={file.file.toString()}
                    {...{ file }}
                    canDelete={canDeleteFiles}
                    canEdit={canEditFiles}
                  />
                ))}
            </div>
          </>
        ) : null}

        {/* Reinspect */}
        {filesByCategory.get('photos_reinspect')?.length ? (
          <>
            <h3 className="font-semibold text-md mt-4 mb-2">Reinspect</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {filesByCategory
                .get('photos_reinspect')
                ?.map((file) => (
                  <File
                    canView={canView}
                    key={file.file.toString()}
                    {...{ file }}
                    canDelete={canDeleteFiles}
                    canEdit={canEditFiles}
                  />
                ))}
            </div>
          </>
        ) : null}

        {/* Other Photos & Videos */}
        {filesByCategory.get('photos_other')?.length ? (
          <>
            <h3 className="font-semibold text-md mt-4 mb-2">Other Photos & Videos</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {filesByCategory
                .get('photos_other')
                ?.map((file) => (
                  <File
                    canView={canView}
                    key={file.file.toString()}
                    {...{ file }}
                    canDelete={canDeleteFiles}
                    canEdit={canEditFiles}
                  />
                ))}
            </div>
          </>
        ) : null}

        {photos.length === 0 && <NotFound>No photos available</NotFound>}
      </Widget>
    </>
  );
}

async function File(props: Readonly<FileProps>) {
  const { canView, file } = props;
  if (!file?.documents?.file) return null;
  const fileDoc = file.documents.file;

  const project = file.$parent?.() as ProjectDoc;
  if (!assertProject(project)) return null;

  const hasSignatures = !!file.signatures?.length;
  const deleteFile = deleteAction.bind(null, project._id.toString(), file.file.toString());
  const renameFileAction = renameFile.bind(null, project._id.toString(), fileDoc._id.toString());
  const moveFileAction = moveFile.bind(null, project._id.toString(), fileDoc._id.toString());

  let nameRender: React.ReactNode = null;

  if (canView) {
    nameRender = (
      <Link className="hover:no-underline underline" href={`/project/${project.id}/files/${fileDoc._id}`}>
        {fileDoc.filename}
      </Link>
    );
  } else {
    nameRender = fileDoc.filename;
  }

  // Check if it's an image or video file
  const isImage = fileDoc.mimetype?.startsWith('image/');
  const isVideo = fileDoc.mimetype?.startsWith('video/');
  const isMediaFile = isImage || isVideo;

  return (
    <ListItem className={isMediaFile ? 'flex flex-col' : ''}>
      <div className="flex items-center gap-6">
        <div className="grow">{nameRender}</div>

        {hasSignatures && (
          <div className="leading-none px-2 py-1 rounded-xl bg-gray-600 text-black text-xs">Has Signatures</div>
        )}

        {isVideo && <div className="leading-none px-2 py-1 rounded-xl bg-blue-600 text-white text-xs">Video</div>}

        <div className="flex">
          {props.canEdit && (
            <>
              <Form action={renameFileAction}>
                <RenameButton projectId={project.id} fileId={fileDoc._id.toString()} filename={fileDoc.filename} />
              </Form>
              <Form action={moveFileAction}>
                <MoveButton
                  projectId={project.id}
                  fileId={fileDoc._id.toString()}
                  currentFolder={file.folder}
                  isMediaFile={isMediaFile}
                />
              </Form>
            </>
          )}
          {props.canDelete && (
            <Form action={deleteFile}>
              <DeleteButton projectId={project.id} fileId={fileDoc._id.toString()} />
            </Form>
          )}
        </div>
      </div>

      {/* Show thumbnail for images */}
      {isImage && canView && (
        <div className="mt-2">
          <Link href={`/project/${project.id}/files/${fileDoc._id}`}>
            <img
              src={`/file/${fileDoc._id}`}
              alt={fileDoc.filename}
              className="w-full h-32 object-cover rounded"
              loading="lazy"
            />
          </Link>
        </div>
      )}

      {/* Show video player for videos */}
      {isVideo && canView && (
        <div className="mt-2">
          <Link href={`/project/${project.id}/files/${fileDoc._id}`}>
            <video
              src={`/file/${fileDoc._id}`}
              className="w-full h-32 object-cover rounded"
              controls
              preload="metadata"
            />
          </Link>
        </div>
      )}
    </ListItem>
  );
}
