import { cache } from 'react';
import mongoose from 'mongoose';

import { getCustomer } from '@/server/customer';
import { getProjectModel } from '@/schemas/projects';

import { validatePermissions } from './permissions';

export const getData = cache(async function (id: string) {
  'use server';
  const { data } = await getCustomer(id);
  const { canEdit, canView, user, account, customer } = await validatePermissions(data);

  // Fetch projects for this customer
  const projectModel = await getProjectModel();
  const projects = await projectModel
    .find({
      customer: new mongoose.Types.ObjectId(id),
      account: user.account,
    })
    .select('_id name milestones assignedTo location created isDead isCancelled')
    .populate(['documents.assignedTo', 'documents.location'])
    .sort({ created: -1 });

  return { canEdit, canView, user, account, customer, projects };
});
