import { cache } from 'react';

import { getDeadLeadReason } from '@/server/dead-lead-reason';

import { edit } from './actions';
import { validatePermissions } from './permissions';

export const getData = cache(async function (id: string) {
  'use server';
  const { data } = await getDeadLeadReason(id);
  const { canEdit, canDelete, user, account, deadLeadReason } = await validatePermissions(data);
  const editAction = edit.bind(null, id, canDelete);

  return { canEdit, canDelete, user, account, deadLeadReason, editAction };
});
