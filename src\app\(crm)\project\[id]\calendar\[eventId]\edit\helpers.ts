import { CALE<PERSON>AR_EVENT_TYPES } from '@/lib/calendar/constants';

export const checkIsDelivery = (type?: string): type is typeof CALENDAR_EVENT_TYPES.DELIVERY => {
  return type === CALENDAR_EVENT_TYPES.DELIVERY;
};
export const checkIsLabor = (type?: string): type is typeof CALENDAR_EVENT_TYPES.LABOR => {
  return type === CALENDAR_EVENT_TYPES.LABOR;
};
export const checkIsAppointment = (type?: string): type is typeof CALENDAR_EVENT_TYPES.APPOINTMENT => {
  return type === CALENDAR_EVENT_TYPES.APPOINTMENT;
};
