import { MoreLink } from '@/lib/nav';
import { ListItem, NotFound, Sticky, Widget } from '@/lib/widget';
import { type ProjectDoc } from '@/schemas';
import { COMMENT_TYPE } from '@/schemas/projects/subdocuments/comment/enums';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { CreateCommentForm } from './create-comment-form';
import { getData, getTopLevelComments } from './helpers';
import { Comment } from './comment';

export async function Comments({ id }: Readonly<{ id: string }>) {
  const { project, canPostComment, revalidateComments } = await getData(id);
  const { comments, id: projectId } = project;

  const internalComments = comments.filter((comment) => comment.type === COMMENT_TYPE.CRM);
  const crewComments = comments.filter((comment) => comment.type === COMMENT_TYPE.CREW_APP);

  // Filter to only get top-level comments (those with no parent)
  const topLevelInternalComments = getTopLevelComments(internalComments);
  const topLevelCrewComments = getTopLevelComments(crewComments);

  return (
    <Tabs defaultValue="internal" className="flex flex-col items-end">
      <TabsList>
        <TabsTrigger className="" value="internal">
          Internal Comments
        </TabsTrigger>
        <TabsTrigger className="" value="crew">
          Crew Comments
        </TabsTrigger>
      </TabsList>
      <TabsContent value="internal" className="w-full">
        <Widget label={`Comments (${internalComments.length})`} grow className="flex flex-col gap-6 h-full">
          <Sticky
            bottom={
              <ListItem>
                <MoreLink href={`/project/${projectId}/comments`}>List Comments</MoreLink>
              </ListItem>
            }
          >
            {!!canPostComment && (
              <CreateCommentForm
                projectId={id}
                commentType={COMMENT_TYPE.CRM}
                revalidateComments={revalidateComments}
              />
            )}
            {topLevelInternalComments.length > 0 ? (
              <div className="w-full">
                {topLevelInternalComments.slice(0, 5).map((comment: ProjectDoc['comments'][number]) => (
                  <Comment key={comment.id} {...{ comment }} />
                ))}
              </div>
            ) : (
              <NotFound>No internal comments available</NotFound>
            )}
          </Sticky>
        </Widget>
      </TabsContent>
      <TabsContent value="crew" className="w-full">
        <Widget label={`Comments (${crewComments.length})`} grow className="flex flex-col gap-6 h-full">
          <Sticky
            bottom={
              <ListItem>
                <MoreLink href={`/project/${projectId}/comments`}>List Comments</MoreLink>
              </ListItem>
            }
          >
            {!!canPostComment && (
              <CreateCommentForm
                projectId={id}
                commentType={COMMENT_TYPE.CREW_APP}
                revalidateComments={revalidateComments}
              />
            )}
            {topLevelCrewComments.length > 0 ? (
              <div className="w-full">
                {topLevelCrewComments.slice(0, 5).map((comment: ProjectDoc['comments'][number]) => (
                  <Comment key={comment.id} {...{ comment }} />
                ))}
              </div>
            ) : (
              <NotFound>No crew comments available</NotFound>
            )}
          </Sticky>
        </Widget>
      </TabsContent>
    </Tabs>
  );
}
