import { Ban, CalendarX, Check, Undo, X } from 'lucide-react';

import { formatDate, getFirstCharacter } from '@/lib/formatting';
import { Widget } from '@/lib/widget';
import { MILESTONEORDER, PROJECTMILESTONE } from '@/schemas/projects/subdocuments';
// import {
//   Stepper,
//   StepperDescription,
//   StepperIndicator,
//   StepperItem,
//   StepperSeparator,
//   StepperTitle,
//   StepperTrigger,
// } from '@/components/ui/stepper';

import { ReasonEdit } from './dead-reason';
import { getData } from './helpers';
import { Icon } from './icon-wrapper';
import { Kill } from './kill';
import { Advance, Recede } from './manual';
import { ReasonView } from './reason-view';
import { MilestonesProps } from './types';

export async function Milestones(props: Readonly<MilestonesProps>) {
  const {
    project,
    advanceMilestoneAction,
    recedeMilestoneAction,
    makeDeadAction,
    makeAliveAction,
    canEdit,
    deadLeadReasons,
  } = await getData(props.id);

  const { modified, leadDeadReason, milestones } = project;

  const current = milestones[milestones.length - 1];

  const isDead = !!project?.isDead;
  const isLead = !!project?.isLead;
  const isClosed = !!project?.isClosed;
  const isCancelled = !!project?.isCancelled;
  const isProspect = !!project?.isProspect;

  let reverseTitle = 'Remove Dead Status';
  if (isClosed) reverseTitle = 'Remove Closed Status';
  if (isCancelled) reverseTitle = 'Remove Cancelled Status';

  let markAsKilled = 'Dead';
  if (!isLead && !isProspect) markAsKilled = 'Cancelled';

  return (
    <Widget
      label={`Milestone: ${current?.name || PROJECTMILESTONE.LEAD}`}
      className="flex items-center select-none"
      action={canEdit && !isDead && !isClosed ? <Advance action={advanceMilestoneAction} /> : null}
      footer={
        !!canEdit ? (
          <div className="flex items-end">
            {isDead || isCancelled ? (
              <ReasonView
                canEdit={canEdit}
                reset={formatDate(modified, true)}
                edit={<ReasonEdit id={props.id}>{leadDeadReason}</ReasonEdit>}
                view={<div>Reason: {leadDeadReason}</div>}
              />
            ) : (
              !isLead && (
                <div>
                  <Recede action={recedeMilestoneAction} />
                </div>
              )
            )}
            <div className="grow" />
            {isDead || isCancelled ? (
              <Kill
                action={makeAliveAction}
                className="cursor-pointer"
                title={reverseTitle}
                deadLeadReasons={deadLeadReasons}
              >
                <Undo className="w-4 h-4" />
              </Kill>
            ) : (
              !isClosed && (
                <Kill
                  action={makeDeadAction}
                  className="cursor-pointer"
                  title={`Mark As ${markAsKilled}`}
                  deadLeadReasons={deadLeadReasons}
                >
                  <Ban className="w-4 h-4" />
                </Kill>
              )
            )}
          </div>
        ) : null
      }
    >
      {/* <div className="w-full flex justify-center">
        <Stepper defaultValue={milestones.length - 1}>
          {MILESTONEORDER.map((milestone, step) => {
            const milestoneFound = milestones.find((s) => s.name === milestone);
            return (
              <StepperItem key={milestone} step={step} className="not-last:flex-1 max-md:items-start">
                <StepperTrigger className="rounded max-md:flex-col">
                  <StepperIndicator />
                  <div className="text-center md:text-left">
                    <StepperTitle>{milestone}</StepperTitle>
                    <StepperDescription className="max-sm:hidden">
                      {!!milestone && (
                        <div className="text-sm">{formatDate(milestoneFound?.date, false, user?.tzoffset)}</div>
                      )}
                    </StepperDescription>
                  </div>
                </StepperTrigger>
                {<StepperSeparator className="max-md:mt-3.5 md:mx-4 bg-black" />}
              </StepperItem>
            );
          })}
        </Stepper>
      </div> */}
      <div className="flex items-center flex-wrap justify-between gap-12 w-full">
        {MILESTONEORDER.map((milestone) => (
          <div key={milestone}>
            <Icon
              isDead={isDead}
              isCancelled={isCancelled}
              label={milestone}
              milestone={milestones.find((s) => s.name === milestone)}
              symbol={<GetSymbol milestone={milestone} />}
            />
          </div>
        ))}

        {!!isDead && (
          <div>
            <Icon
              isDead={isDead}
              isCancelled={isCancelled}
              label={PROJECTMILESTONE.DEAD}
              milestone={milestones.find((s) => s.name === PROJECTMILESTONE.DEAD)}
              symbol={<CalendarX className="w-12 h-12" />}
            />
          </div>
        )}

        {!!isCancelled && (
          <div>
            <Icon
              isDead={isDead}
              isCancelled={isCancelled}
              label={PROJECTMILESTONE.CANCELLED}
              milestone={milestones.find((s) => s.name === PROJECTMILESTONE.CANCELLED)}
              symbol={<X className="w-12 h-12" />}
            />
          </div>
        )}
      </div>
    </Widget>
  );
}

function GetSymbol(props: Readonly<{ milestone: PROJECTMILESTONE }>) {
  if (props.milestone === PROJECTMILESTONE.CLOSED) return <Check className="w-12 h-12" />;
  if (props.milestone === PROJECTMILESTONE.CANCELLED) return <X className="w-12 h-12" />;

  return getFirstCharacter(props.milestone);
}
