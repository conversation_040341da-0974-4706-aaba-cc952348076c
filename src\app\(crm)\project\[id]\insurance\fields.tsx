'use client';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Browser<PERSON><PERSON>ch, type BrowserSelectOption } from '@/lib/form';
import { SCHEMAS } from '@/lib/schemas';

export function InsuranceCompanyField({ value }: Readonly<{ value?: BrowserSelectOption }>) {
  return (
    <BrowserField
      headers={{ name: 'Name' }}
      label="Insurance Company"
      name="insuranceCompany"
      required
      value={value}
      callback={BrowserSearch({ model: SCHEMAS.INSURANCE })}
    />
  );
}
