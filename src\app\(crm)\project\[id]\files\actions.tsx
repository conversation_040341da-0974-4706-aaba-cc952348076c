import mongoose from 'mongoose';
import { revalidatePath } from 'next/cache';

import { db } from '@/lib';
import { ActionResult } from '@/lib/form';
import { getFileModel, getProjectModel } from '@/schemas';
import { upload } from '@/server';

import { getData } from './helpers';

export async function deleteAction(projectId: string, fileId: string): Promise<ActionResult> {
  'use server';
  const { canDeleteFiles } = await getData(projectId);
  if (!canDeleteFiles) return { error: 'Forbidden: You do not have permission to delete files.' };

  try {
    await db();
    const projectModel = await getProjectModel();
    const project = await projectModel.findOne({ _id: new mongoose.mongo.ObjectId(projectId) });
    if (!project) throw new Error('No Project');

    project.set(
      'files',
      (project.files || []).filter((file) => {
        return file.file.toString() !== fileId;
      }),
    );

    await project.save({ timestamps: false });
  } catch (e) {
    if (e instanceof Error) {
      return { error: e.message };
    }

    return { error: 'Unknown error' };
  }

  revalidatePath(`/project/${projectId}`);

  return { error: null };
}

export async function uploadFile(id: string, _: unknown, formData: FormData): Promise<ActionResult> {
  'use server';

  // Handle multiple file uploads (FormData with multiple files will have multiple entries with the same name)
  const files = formData.getAll('file') as File[];

  // Number of files received for upload

  if (!files.length || files.every((file) => file.size === 0)) {
    return { error: 'Please choose at least one file to upload' };
  }

  const { canCreateFiles } = await getData(id);
  if (!canCreateFiles) return { error: 'Forbidden: You do not have permission to upload files.' };

  try {
    await db();
    const projectModel = await getProjectModel();

    const project = await projectModel.findOne({ _id: new mongoose.mongo.ObjectId(id) });
    if (!project) throw new Error('No Project');

    const folder = (formData.get('folder') as string) || '/';

    // Upload each file
    for (const file of files) {
      if (file.size > 0) {
        const fileId = await upload({ file, protected: true });
        if (fileId) {
          project.files.push({ file: fileId, folder });
        }
      }
    }

    await project.save({ timestamps: false });
  } catch (e) {
    if (e instanceof Error) {
      return { error: e.message };
    }

    return { error: 'Unknown error' };
  }

  revalidatePath(`/project/${id}`);

  return { error: null };
}

export async function renameFile(
  projectId: string,
  fileId: string,
  _: unknown,
  formData: FormData,
): Promise<ActionResult> {
  'use server';
  const { canEditFiles } = await getData(projectId);
  if (!canEditFiles) return { error: 'Forbidden: You do not have permission to rename files.' };

  // Extract the new filename from the form data
  const newFilename = formData.get('newFilename') as string;
  if (!newFilename || newFilename.trim() === '') {
    return { error: 'Filename cannot be empty' };
  }

  try {
    await db();
    const fileModel = await getFileModel();

    // Find the file and update its filename
    const file = await fileModel.findOne({ _id: new mongoose.mongo.ObjectId(fileId) });
    if (!file) throw new Error('File not found');

    file.set('filename', newFilename.trim());
    await file.save();
  } catch (e) {
    if (e instanceof Error) {
      return { error: e.message };
    }

    return { error: 'Unknown error' };
  }

  revalidatePath(`/project/${projectId}`);

  return { error: null };
}

export async function moveFile(
  projectId: string,
  fileId: string,
  _: unknown,
  formData: FormData,
): Promise<ActionResult> {
  'use server';
  const { canEditFiles } = await getData(projectId);
  if (!canEditFiles) return { error: 'Forbidden: You do not have permission to move files.' };

  // Extract the new folder from the form data
  const newFolder = formData.get('newFolder') as string;
  if (!newFolder) {
    return { error: 'New folder is required' };
  }

  try {
    await db();
    const projectModel = await getProjectModel();

    const project = await projectModel.findOne({ _id: new mongoose.mongo.ObjectId(projectId) });
    if (!project) throw new Error('Project not found');

    // Find the file in the project's files array and update its folder
    const fileIndex = project.files.findIndex((file) => file.file.toString() === fileId);
    if (fileIndex === -1) throw new Error('File not found in project');

    project.files[fileIndex].folder = newFolder;
    await project.save({ timestamps: false });
  } catch (e) {
    if (e instanceof Error) {
      return { error: e.message };
    }

    return { error: 'Unknown error' };
  }

  revalidatePath(`/project/${projectId}`);

  return { error: null };
}
