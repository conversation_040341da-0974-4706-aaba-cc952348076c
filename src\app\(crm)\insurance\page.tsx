import Link from 'next/link';

import { formatDate } from '@/lib/formatting';
import { ListScreen } from '@/lib/screen';
import { SCHEMAS } from '@/lib/schemas';
import { Cell } from '@/lib/table';
import type { InsuranceCompanyDoc } from '@/schemas';
import { validateRequest } from '@/server/auth';

export default async function ListInsurances({ searchParams }: { searchParams: { page?: string } }) {
  const { user } = await validateRequest();

  return (
    <ListScreen<InsuranceCompanyDoc>
      {...{ searchParams }}
      model={SCHEMAS.INSURANCE}
      label="Insurances"
      heading={
        <>
          <Cell>Name</Cell>
          <Cell>Created</Cell>
        </>
      }
      body={async (item) => {
        return (
          <>
            <Cell scope="row">
              <Link href={`/insurance/${item.id}`} className="hover:underline">
                {item.name}
              </Link>
            </Cell>
            <Cell>{formatDate(item.created, true, user?.tzoffset)}</Cell>
          </>
        );
      }}
    />
  );
}
