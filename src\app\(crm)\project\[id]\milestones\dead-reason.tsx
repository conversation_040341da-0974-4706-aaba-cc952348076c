import { Pencil } from 'lucide-react';

import { Field, Form, FormButton } from '@/lib/form';

import { getData } from './helpers';
import { ReasonEditProps } from './types';

export async function ReasonEdit(props: Readonly<ReasonEditProps>) {
  const { deadLeadReasons, canEdit, deadReasonAction } = await getData(props.id);
  const currentDeadLeadReason = deadLeadReasons.find((deadLeadReason) => deadLeadReason.name === props.children);

  if (!canEdit && props.children) {
    return <div className="text-sm">Reason: {props.children}</div>;
  } else if (!canEdit && !props.children) {
    return '';
  }

  return (
    <Form action={deadReasonAction}>
      <div className="flex items-end gap-4">
        {/* <Field name="leadDeadReason" type="text" label="Reason" value={props.children || ''} /> */}

        <Field
          name="leadDeadReason"
          label="Reason"
          type="select"
          value={
            currentDeadLeadReason ? { label: currentDeadLeadReason.name, value: currentDeadLeadReason.name } : undefined
          }
          options={deadLeadReasons.map((deadLeadReason) => ({
            label: deadLeadReason.name,
            value: deadLeadReason.name,
          }))}
        />
        <FormButton>
          <Pencil className="h-4 w-4 -my-4" />
        </FormButton>
      </div>
    </Form>
  );
}
