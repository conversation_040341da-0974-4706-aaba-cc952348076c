import Link from 'next/link';

import { DisplayData } from '@/lib/view';
import { Widget } from '@/lib/widget';
import { ActionFooter } from '@/lib/nav';
import { formatDate } from '@/lib/formatting';
import { CALENDAR_EVENTS_DICTIONARY } from '@/lib/calendar/constants';

import { getData } from './helpers';

export default async function ViewCalendarEvent({ params }: Readonly<{ params: { id: string } }>) {
  const { calendarEvent } = await getData(params.id);

  return (
    <div className="space-y-6">
      <Widget label={calendarEvent.title}>
        <DisplayData label="Event Type" value={CALENDAR_EVENTS_DICTIONARY[calendarEvent.type]} />
        <DisplayData label="Timeline" value={formatDate(calendarEvent.start, true)} />
        <DisplayData label="End" value={formatDate(calendarEvent.end, true)} />
      </Widget>

      <ActionFooter left={<Link href="/calendar">Back to Calendar</Link>} />
    </div>
  );
}
