import Link from 'next/link';

import { formatDate } from '@/lib/formatting';
import { ListScreen } from '@/lib/screen';
import { SCHEMAS } from '@/lib/schemas';
import { Cell } from '@/lib/table';
import type { DeadLeadReasonDoc } from '@/schemas';
import { validateRequest } from '@/server/auth';

export default async function ListDeadLeadReasons({ searchParams }: { searchParams: { page?: string } }) {
  const { user } = await validateRequest();

  return (
    <ListScreen<DeadLeadReasonDoc>
      {...{ searchParams }}
      model={SCHEMAS.DEAD_LEAD_REASON}
      label="Dead Lead Reasons"
      heading={
        <>
          <Cell>Name</Cell>
          <Cell>Created</Cell>
        </>
      }
      body={async (item) => {
        return (
          <>
            <Cell scope="row">
              <Link href={`/dead-lead-reason/${item.id}`} className="hover:underline">
                {item.name}
              </Link>
            </Cell>
            <Cell>{formatDate(item.created, true, user?.tzoffset)}</Cell>
          </>
        );
      }}
    />
  );
}
