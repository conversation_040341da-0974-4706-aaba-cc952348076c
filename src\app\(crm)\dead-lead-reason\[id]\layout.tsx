import { Metadata, ResolvingMetadata } from 'next/types';

import { getData } from './helpers';
import { ViewDeadLeadReasonProps } from './types';

export async function generateMetadata(
  { params }: ViewDeadLeadReasonProps,
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const { deadLeadReason } = await getData(params.id);

  const name = deadLeadReason.name || deadLeadReason.id;
  const parentMetadata = await parent;
  const title = [name, parentMetadata.title?.absolute].filter((x) => !!x);

  return {
    title: title.join(' | '),
  };
}

export default function DeadLeadReasonIdLayout({ children }: Readonly<{ children: React.ReactNode }>) {
  return children;
}
