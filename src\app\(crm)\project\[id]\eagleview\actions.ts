'use server';

import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

import { ActionResult } from '@/lib/form';
import { getProject } from '@/server/projects';
import { validateEnum } from '@/lib/typescript';
import { UNIT } from '@/schemas/quote-template';
import { EagleviewApi, EAGLEVIEW_FILE_FORMAT, EAGLEVIEW_FILE_TYPE } from '@/services/eagleview';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';
import { upload } from '@/server';

import { serializeMeasurements } from './helpers';

export const edit = async (id: string, _: unknown, formData: FormData): Promise<ActionResult> => {
  const { data: project } = await getProject(id);
  if (!project) return { error: 'Project not found' };

  const name = formData.get('name')?.toString() || '';
  const value = formData.get('value')?.toString() || '';
  const unit = formData.get('unit')?.toString() || '';

  if (!validateEnum(unit, UNIT)) return { error: 'Invalid unit' };

  const existingDimensionIndex = project.dimensions.findIndex((dimension) => dimension.name === name);

  let updatedDimensions;
  if (existingDimensionIndex !== -1) {
    // Update existing dimension
    updatedDimensions = [...project.dimensions];
    const existingDimension = updatedDimensions[existingDimensionIndex];
    existingDimension.value = value;
    existingDimension.unit = unit;
  } else {
    // Add new dimension
    updatedDimensions = [{ name, value, unit }, ...project.dimensions];
  }

  const mergedDimensions = [{ name, value, unit }, ...project.dimensions];
  const names = mergedDimensions.map((dimension) => dimension.name);

  project.set(
    'dimensions',
    mergedDimensions
      .map((dimension, index) => {
        if (names.indexOf(dimension.name) !== index) return null;
        return dimension;
      })
      .filter((x) => !!x),
  );
  await project.save({ timestamps: false });

  revalidatePath(`/project/${id}`);

  return { error: null };
};

export const copyToDimensions = async (id: string) => {
  const { data: project } = await getProject(id);
  if (!project) return { error: 'Project not found' };

  if (!project.eagleview) return { error: 'Eagleview report not found' };

  const eagleviewApi = new EagleviewApi(project.account);
  const report = await eagleviewApi.getReport(Number(project.eagleview.reportId));
  const dimensions = serializeMeasurements(report.TotalMeasurements).map((dimension) => {
    return {
      name: dimension.name,
      value: dimension.value,
      unit: dimension.unit,
    };
  });

  project.set('dimensions', dimensions);
  await project.save({ timestamps: false });

  revalidatePath(`/project/${id}`);

  return { error: null };
};

const saveReportDoc = async (docLink: string, projectName: string) => {
  const document = await fetch(docLink).then((res) => res.blob());

  const fileId = await upload({
    file: new File([document], `${projectName}-Eagleview-Report.pdf`, { type: 'application/pdf' }),
    protected: true,
  });

  return fileId;
};

export const requestReport = async (id: string) => {
  const { user, account } = await validateRequest();

  if (!user || !account) throw new ServerError('Unauthorized');

  const { data: project } = await getProject(id);
  if (!project) return { error: 'Project not found' };

  try {
    const eagleviewApi = new EagleviewApi(project.account);

    if (!project.address) return { error: 'Project address not found' };

    const order = await eagleviewApi.placeOrder({ address: project.address });
    const report = await eagleviewApi.getReport(Number(order.ReportIds[0]));

    project.set('eagleview', {
      reportId: report.ReportId,
      orderId: order.OrderId,
      createdBy: user._id,
      created: new Date(),
      modifiedBy: user._id,
      modified: new Date(),
      status: report.Status,
    });

    if (report.Status === 'Completed') {
      const fileId = await saveReportDoc(report.ReportDownloadLink, project.name);

      project.files.push({ file: fileId, folder: '/' });
    }

    await project.save({ timestamps: false });
  } catch (error) {
    return { error: 'Failed to request report' };
  }

  revalidatePath(`/project/${id}`);
  redirect(`/project/${id}/eagleview`);
};

export const updateReportStatus = async (id: string) => {
  const { data: project } = await getProject(id);
  if (!project) return { error: 'Project not found' };

  if (!project.eagleview) return { error: 'Eagleview report not found' };

  const { user } = await validateRequest();
  if (!user) throw new ServerError('Unauthorized');

  const eagleviewApi = new EagleviewApi(project.account);
  const report = await eagleviewApi.getReport(Number(project.eagleview.reportId));

  //? saves report status change
  if (report.Status === 'Completed' && project.eagleview.status === 'In Progress') {
    const fileId = await saveReportDoc(report.ReportDownloadLink, project.name);

    project.files.push({ file: fileId, folder: '/' });
  }

  project.set('eagleview', {
    ...project.eagleview,
    modifiedBy: user._id,
    modified: new Date(),
    status: report.Status,
  });

  await project.save({ timestamps: false });

  revalidatePath(`/project/${id}`);

  return { error: null };
};

export const getReport = async (id: string) => {
  const { data: project } = await getProject(id);

  if (!project) return { error: 'Project not found' };
  if (!project.eagleview) return { error: 'Eagleview report not found' };

  const eagleviewApi = new EagleviewApi(project.account);
  const report = project.eagleview;
  const image = await eagleviewApi.getFileReport(Number(project.eagleview.reportId), {
    fileFormat: EAGLEVIEW_FILE_FORMAT.JPG,
    fileType: EAGLEVIEW_FILE_TYPE.TOP_IMAGE,
  });
  const base64 = `data:image/jpeg;base64,${Buffer.from(image).toString('base64')}`;

  return { image: base64, report };
};
