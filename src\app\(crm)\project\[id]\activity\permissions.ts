import { notFound } from 'next/navigation';
import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { ProjectDoc } from '@/schemas';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function (project: ProjectDoc | null) {
  'use server';
  const { user, account, isSystemAdmin } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not edit project.');

  const sameAccount = account._id.equals(project?.account) || isSystemAdmin;

  const canView = (await can(user, 'view_project')) && sameAccount;
  if (!canView) throw new ServerError('Forbidden: you can not edit project.');

  const [canEdit, canViewUsers, canViewLocations] = await Promise.all([
    can(user, 'edit_project'),
    can(user, 'view_user'),
    can(user, 'view_location'),
  ]);

  if (!project?._id) notFound();

  return { canView, user, canEdit, account, project, canViewUsers, canViewLocations };
});
