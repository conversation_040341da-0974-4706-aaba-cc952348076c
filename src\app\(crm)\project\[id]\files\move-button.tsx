'use client';

import { FolderUp } from 'lucide-react';
import { useState } from 'react';
import { useFormStatus } from 'react-dom';

import { Spinner } from '@/lib/form';
import { FILE_FOLDER } from '@/schemas/projects/subdocuments/file/enums';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

export function MoveButton({
  projectId,
  fileId,
  currentFolder,
  isMediaFile,
}: Readonly<{
  projectId: string;
  fileId: string;
  currentFolder: string;
  isMediaFile: boolean;
}>) {
  const [isMoving, setIsMoving] = useState(false);
  const { pending } = useFormStatus();

  if (isMoving) {
    return (
      <div className="flex items-center">
        <Select
          name="newFolder"
          defaultValue={currentFolder}
          // className="bg-slate-700 text-white rounded-md px-2 py-1 text-sm mr-2"
        >
          <SelectTrigger>
            <SelectValue placeholder="Select" />
          </SelectTrigger>
          <SelectContent>
            {isMediaFile ? (
              // Photo/video categories
              <>
                <SelectItem value={FILE_FOLDER.PHOTOS_OTHER}>Other Photos</SelectItem>
                <SelectItem value={FILE_FOLDER.PHOTOS_JOBSITE_BEFORE}>Job Site - Before</SelectItem>
                <SelectItem value={FILE_FOLDER.PHOTOS_JOBSITE_AFTER}>Job Site - After</SelectItem>
                <SelectItem value={FILE_FOLDER.PHOTOS_MATERIAL_CHECK}>Material Check</SelectItem>
                <SelectItem value={FILE_FOLDER.PHOTOS_DAMAGE}>Damage</SelectItem>
                <SelectItem value={FILE_FOLDER.PHOTOS_SUPPLEMENT}>Supplement</SelectItem>
                <SelectItem value={FILE_FOLDER.PHOTOS_REINSPECT}>Reinspect</SelectItem>
              </>
            ) : (
              // Document categories
              <>
                <SelectItem value={FILE_FOLDER.ROOT}>Uncategorized</SelectItem>
                <SelectItem value={FILE_FOLDER.JOB_PAPERWORK}>Job Paperwork</SelectItem>
                <SelectItem value={FILE_FOLDER.EMAIL_DOCUMENTS}>Email Documents</SelectItem>
                <SelectItem value={FILE_FOLDER.PERMITS}>Permits</SelectItem>
                <SelectItem value={FILE_FOLDER.ROOF_REPORT}>Roof Report</SelectItem>
                <SelectItem value={FILE_FOLDER.LABOR_INVOICES}>Labor Invoices</SelectItem>
                <SelectItem value={FILE_FOLDER.MATERIAL_INVOICES}>Material Invoices</SelectItem>
              </>
            )}
          </SelectContent>
        </Select>
        <input type="hidden" value={projectId} name="projectId" readOnly />
        <input type="hidden" value={fileId} name="fileId" readOnly />
        <Button
          type="submit"
          disabled={pending}
          className="bg-green-600 flex w-6 h-6 justify-center items-center cursor-pointer rounded-lg mr-1"
        >
          {pending ? <Spinner height="h-4" /> : <FolderUp className="w-4 h-4" />}
        </Button>
        <Button
          type="button"
          onClick={() => setIsMoving(false)}
          className="bg-gray-600 flex w-6 h-6 justify-center items-center cursor-pointer rounded-lg"
        >
          ✕
        </Button>
      </div>
    );
  }

  return (
    <Button
      type="button"
      onClick={() => setIsMoving(true)}
      className="bg-purple-600 flex w-6 h-6 justify-center items-center cursor-pointer rounded-lg mr-1"
    >
      <FolderUp className="w-4 h-4" />
    </Button>
  );
}
