import { cache } from 'react';

import { getProject } from '@/server/projects';

import { edit } from './actions';
import { validatePermissions } from './permissions';

export const getData = cache(async function (projectId: string, quoteId: string) {
  'use server';
  const { data } = await getProject(projectId);
  const props = await validatePermissions(
    data,
    data?.quotes.find((q) => q._id.equals(quoteId)),
  );
  const editAction = edit.bind(null, projectId, quoteId);

  return { ...props, editAction };
});
