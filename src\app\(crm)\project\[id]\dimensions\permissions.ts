import { notFound } from 'next/navigation';
import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { ProjectDoc } from '@/schemas';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function (project: ProjectDoc | null) {
  'use server';
  const { user, account, isSystemAdmin } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not edit projects.');

  const sameAccount = account._id.equals(project?.account) || isSystemAdmin;

  const canView = await can(user, 'view_project_dimensions', sameAccount);
  if (!canView) throw new ServerError('Forbidden: you can not view project dimensions.');

  const canEdit = await can(user, 'edit_project_dimensions', sameAccount);

  if (!project?._id) notFound();

  return { user, account, project, canView, canEdit };
});
