import { cache } from 'react';

import { getProject } from '@/server/projects';

import { edit } from './actions';
import { validatePermissions } from './permissions';

export const getData = cache(async function (projectId: string, invoiceId: string) {
  'use server';

  const { data } = await getProject(projectId);
  const props = await validatePermissions(data, invoiceId);

  const editAction = edit.bind(null, projectId, invoiceId);

  return { ...props, editAction };
});
