import mongoose from 'mongoose';
import { redirect } from 'next/navigation';

import { db } from '@/lib';
import { ActionResult } from '@/lib/form';
import { assertString } from '@/lib/validators';
import { getCustomerModel } from '@/schemas';

export async function edit(id: string, canDelete: boolean, _: unknown, formData: FormData): Promise<ActionResult> {
  'use server';
  const { _action } = Object.fromEntries(formData);

  if (_action === 'delete') {
    if (!canDelete) return { error: 'Forbidden: you do not have permissions to delete this record' };

    try {
      await db();
      const customerModel = await getCustomerModel();
      await customerModel.deleteOne({ _id: new mongoose.mongo.ObjectId(id) });
    } catch (e) {
      if (e instanceof Error) return { error: e.message };

      return { error: 'Unknown error' };
    }

    redirect(`/customer`);
  }

  try {
    await db();
    const customerModel = await getCustomerModel();
    const document = await customerModel.findOne({ _id: new mongoose.mongo.ObjectId(id) });
    if (!document) throw new Error('Not Found');

    const { firstName, lastName, phone, email, balance, deposit, overdue, unbilled } = Object.fromEntries(formData);

    if (assertString(firstName)) document.firstName = firstName;
    if (assertString(lastName)) document.lastName = lastName;
    if (assertString(phone)) document.phoneNumbers = [phone];
    if (assertString(email)) document.emailAddresses = [email];
    if (assertString(balance)) document.set('invoice.balance', balance);
    if (assertString(deposit)) document.set('invoice.deposit', deposit);
    if (assertString(overdue)) document.set('invoice.overdue', overdue);
    if (assertString(unbilled)) document.set('invoice.unbilled', unbilled);

    await document.save();
  } catch (e) {
    if (e instanceof Error) return { error: e.message };

    return { error: 'Unknown error' };
  }

  redirect(`/customer/${id}`);
}
