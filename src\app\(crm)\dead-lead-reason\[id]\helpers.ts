import { cache } from 'react';

import { getDeadLeadReason } from '@/server/dead-lead-reason';

import { validatePermissions } from './permissions';

export const getData = cache(async function (id: string) {
  'use server';
  const { data } = await getDeadLeadReason(id);
  const { canEdit, canView, user, account, deadLeadReason } = await validatePermissions(data);

  return { canEdit, canView, user, account, deadLeadReason };
});
