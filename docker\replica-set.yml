services:
  mongo1:
    image: mongo
    ports:
      - 127.0.0.1:27017:27017
    entrypoint: ['mongod', '--bind_ip_all', '--replSet', 'rs0', '--port', '27017']
    volumes:
      - mongo1:/data/db
    expose:
      - 27017
    restart: on-failure

  mongo2:
    image: mongo
    ports:
      - 127.0.0.1:27018:27018
    entrypoint: ['mongod', '--bind_ip_all', '--replSet', 'rs0', '--port', '27018']
    volumes:
      - mongo2:/data/db
    restart: on-failure

  mongo3:
    image: mongo
    ports:
      - 127.0.0.1:27019:27019
    entrypoint: ['mongod', '--bind_ip_all', '--replSet', 'rs0', '--port', '27019']
    volumes:
      - mongo3:/data/db
    restart: on-failure

  init:
    depends_on:
      - mongo1
      - mongo2
      - mongo3
    image: mongo
    restart: on-failure
    volumes:
      - ./init.sh:/etc/init.sh
    entrypoint: ['./etc/init.sh']

  mongo-express:
    depends_on:
      - init
    image: mongo-express
    restart: always
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: root
      ME_CONFIG_MONGODB_ADMINPASSWORD: example
      ME_CONFIG_MONGODB_URL: ***************************************************************************
      ME_CONFIG_BASICAUTH: false
    ports:
      - 127.0.0.1:8090:8081

volumes:
  mongo1:
  mongo2:
  mongo3:
