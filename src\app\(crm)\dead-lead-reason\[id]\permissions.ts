import { notFound } from 'next/navigation';
import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { DeadLeadReasonDoc } from '@/schemas';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function (deadLeadReason: DeadLeadReasonDoc | null) {
  'use server';
  const { user, account, isSystemAdmin } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not edit dead lead reason.');

  const sameAccount = account._id.equals(deadLeadReason?.account) || isSystemAdmin;

  const canView = await can(user, 'view_deadleadreason', sameAccount);
  if (!canView) throw new ServerError('Forbidden: you can not edit dead lead reason.');

  const canEdit = await can(user, 'edit_deadleadreason');

  if (!deadLeadReason?._id) notFound();

  return { canView, canEdit, user, account, deadLeadReason };
});
