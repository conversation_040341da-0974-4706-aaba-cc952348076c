import Link from 'next/link';

import { DisplayData } from '@/lib/view';
import { EditIcon, Widget } from '@/lib/widget';
import { ActionFooter } from '@/lib/nav';

import { getData } from './helpers';
import { ViewDeadLeadReasonProps } from './types';

export default async function ViewDeadLeadReason({ params }: ViewDeadLeadReasonProps) {
  const { deadLeadReason, canEdit, account } = await getData(params.id);

  const editLink = `/dead-lead-reason/${deadLeadReason._id}/edit`;

  return (
    <div className="space-y-4">
      <Widget label={deadLeadReason.name} action={canEdit && <EditIcon href={editLink} />}>
        <DisplayData label="Name" value={deadLeadReason.name} />
      </Widget>
      <ActionFooter
        left={<Link href={`/account/${account._id.toString()}/custom-fields`}>Back to Dead Lead Reasons</Link>}
      />
    </div>
  );
}
