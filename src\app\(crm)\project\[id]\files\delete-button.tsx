'use client';

import { Trash2 } from 'lucide-react';
import { useFormStatus } from 'react-dom';

import { Spinner } from '@/lib/form';
import { Button } from '@/components/ui/button';

export function DeleteButton({ projectId, fileId }: Readonly<{ projectId: string; fileId: string }>) {
  const { pending } = useFormStatus();

  return (
    <Button
      type="submit"
      disabled={pending}
      className="bg-red-600 flex w-6 h-6 justify-center items-center cursor-pointer rounded-lg mr-3"
    >
      <input type="hidden" value={projectId} name="_id" readOnly />
      <input type="hidden" value={fileId} name="_id_file" readOnly />

      {pending ? <Spinner height="h-4" /> : <Trash2 className="w-4 h-4" />}
    </Button>
  );
}
