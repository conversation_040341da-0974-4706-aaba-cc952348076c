import Link from 'next/link';

import { DisplayData } from '@/lib/view';
import { EditIcon, Widget } from '@/lib/widget';
import { ActionFooter } from '@/lib/nav';

import { getData } from './helpers';
import { ViewInsuranceProps } from './types';

export default async function ViewInsurance({ params }: ViewInsuranceProps) {
  const { insurance, canEdit, account } = await getData(params.id);

  const editLink = `/insurance/${insurance._id}/edit`;

  return (
    <div className="space-y-4">
      <Widget label={insurance.name} action={canEdit && <EditIcon href={editLink} />}>
        <DisplayData label="Name" value={insurance.name} />
      </Widget>
      <ActionFooter left={<Link href={`/account/${account._id.toString()}/custom-fields`}>Back to Insurances</Link>} />
    </div>
  );
}
