import { notFound } from 'next/navigation';
import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { CustomerDoc } from '@/schemas';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function (customer: CustomerDoc | null) {
  'use server';
  const { user, account, isSystemAdmin } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not edit customers.');

  const sameAccount = account._id.equals(customer?.account) || isSystemAdmin;

  const canEdit = await can(user, 'edit_Customer', sameAccount);
  if (!canEdit) throw new ServerError('Forbidden: you can not edit customers.');

  const canDelete = await can(user, 'delete_Customer', sameAccount);

  if (!customer?._id) notFound();

  return { canEdit, canDelete, user, account, customer };
});
