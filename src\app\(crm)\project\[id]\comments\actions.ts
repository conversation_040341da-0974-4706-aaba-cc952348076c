import mongoose from 'mongoose';
import { revalidatePath } from 'next/cache';

import { db } from '@/lib';
import { type ActionResult } from '@/lib/form';
import { assertString } from '@/lib/validators';
import { NOTIFTYPE, getNotificationModel, getProjectModel, type Project } from '@/schemas';
import { COMMENT_TYPE, PROJECTACTIVITYTYPES } from '@/schemas/projects/subdocuments';

import { getData } from './helpers';

export async function postComment(
  id: string,
  type: COMMENT_TYPE,
  _: unknown,
  formData: FormData,
): Promise<ActionResult> {
  'use server';
  const { canPostComment, user } = await getData(id);
  if (!canPostComment) return { error: 'Forbidden: You do not have permission to post comments.' };

  try {
    await db();
    const projectModel = await getProjectModel();
    const notificationModel = await getNotificationModel();

    const project = await projectModel
      .findOne({ _id: new mongoose.mongo.ObjectId(id) })
      .populate({ path: 'comments', populate: 'documents.user' });
    if (!project) throw new Error('No Project');

    const unFilteredUsers = project?.comments
      ?.map((comment: Project['comments'][number]) => {
        return comment?.documents?.user;
      })
      .map((user) => user?._id)
      .filter((u) => !!u);

    if (project.assignedTo) unFilteredUsers.push(project.assignedTo);

    const users = [...new Set(unFilteredUsers)];

    const commentFormdata = formData.get('comment');
    if (assertString(commentFormdata)) {
      const trimmedComment = commentFormdata.trim();
      if (!trimmedComment) {
        throw new Error('No comment submitted');
      }

      const mentions: mongoose.mongo.ObjectId[] = [];
      const formDataMentions = formData.getAll('comment[mention][]');
      if (formDataMentions && formDataMentions.length > 0) {
        formDataMentions.forEach((mention) => {
          if (assertString(mention)) {
            mentions.push(new mongoose.mongo.ObjectId(mention));
          }
        });
      }

      // Check if this is a reply to another comment
      const parentCommentId = formData.get('parentComment');
      let parentComment = null;

      if (parentCommentId && assertString(parentCommentId)) {
        parentComment = new mongoose.mongo.ObjectId(parentCommentId);

        // Verify the parent comment exists
        const parentExists = project.comments.some((comment) => comment._id.toString() === parentCommentId);

        if (!parentExists) {
          parentComment = null;
          // Log warning about missing parent comment
          // Replace with a custom logger if needed
          // logger.warn('Parent comment not found:', parentCommentId);
        } else {
          // Replace with a custom logger if needed
          // logger.info('Creating reply to comment:', parentCommentId);
        }
      }

      project.comments.push({
        _id: new mongoose.mongo.ObjectId(),
        value: trimmedComment,
        user: user._id,
        mentions,
        created: new Date(),
        type,
        parentComment,
      });
      project.markModified('comments');

      const userMentionIds = mentions.map((mention) => mention.toString());
      const usersWithoutMention = users.filter((u) => !userMentionIds.includes(u.toString()));

      if (usersWithoutMention.length) {
        for (const notifuser of usersWithoutMention) {
          if (!notifuser?.equals(user.id)) {
            await notificationModel.create({
              account: project.account,
              user: notifuser,
              link: '/project/' + project._id,
              message: 'Comment: ' + trimmedComment,
              type: NOTIFTYPE.PROJECT,
            });
          }
        }
      }

      if (mentions.length) {
        for (const mention of mentions) {
          await notificationModel.create({
            account: project.account,
            user: mention,
            link: '/project/' + project._id,
            message: `Mentioned by ${user.name} on ${project.name}`,
            type: NOTIFTYPE.MENTION,
          });
        }
      }

      // If this is a reply, also send a notification to the parent comment author
      if (parentComment) {
        const parentCommentObj = project.comments.find((comment) => comment._id.toString() === parentCommentId);

        if (parentCommentObj && !parentCommentObj.user.equals(user._id)) {
          await notificationModel.create({
            account: project.account,
            user: parentCommentObj.user,
            link: '/project/' + project._id,
            message: `${user.name} replied to your comment`,
            type: NOTIFTYPE.MENTION,
          });
        }
      }
      // Add activity history entry for the comment
      const commentPreview = trimmedComment.length > 50 ? `${trimmedComment.substring(0, 50)}...` : trimmedComment;

      const activityValue = parentComment
        ? `Added reply to a comment: ${commentPreview}`
        : `Added comment: ${commentPreview}`;

      // Add activity for the new comment
      project.activityHistory.push({
        _id: new mongoose.mongo.ObjectId(),
        type: PROJECTACTIVITYTYPES.COMMENT,
        user: user._id,
        value: activityValue,
        created: new Date(),
      });
    } else {
      throw new Error('No comment submitted');
    }

    await project.save({ timestamps: false });
  } catch (e) {
    if (e instanceof Error) {
      return { error: e.message };
    }

    return { error: 'Unknown error' };
  }

  revalidatePath(`/project/${id}`);

  return { error: null, reset: true };
}

export async function deleteComment(prevState: unknown, form: FormData): Promise<ActionResult> {
  'use server';

  const id = form.get('id') as string;
  const commentId = form.get('comment_id') as string;

  const { canDeleteComment, user } = await getData(id);
  if (!canDeleteComment) return { error: 'Forbidden: You do not have permission to delete comments.' };

  try {
    await db();
    const projectModel = await getProjectModel();
    const project = await projectModel.findOne({ _id: new mongoose.mongo.ObjectId(id) });
    if (!project) throw new Error('No Project');

    project.set(
      'comments',
      project.comments.filter((comment) => {
        return comment._id.toString() !== commentId;
      }),
    );

    // Add activity for comment deletion with proper type
    const activityId = new mongoose.mongo.ObjectId();
    project.activityHistory.push({
      _id: activityId,
      type: PROJECTACTIVITYTYPES.COMMENT,
      user: user._id,
      value: 'Deleted a comment',
      created: new Date(),
    });

    // Manually attach the user document to ensure it's available for UI rendering
    // Ensure activity history is properly initialized if needed
    if (!Array.isArray(project.activityHistory)) {
      project.activityHistory = project.activityHistory || [];
    }

    // Find the newly added activity and attach the user document
    const activity = project.activityHistory.find((a) => a._id.equals(activityId));
    if (activity) {
      activity.documents = { user };
    }

    await project.save({ timestamps: false });
  } catch (e) {
    if (e instanceof Error) {
      return { error: e.message };
    }

    return { error: 'Unknown error' };
  }

  revalidatePath(`/project/${id}`);

  return { error: null };
}

export async function revalidateComments(id: string): Promise<ActionResult> {
  'use server';
  revalidatePath(`/project/${id}`);

  return { error: null, reset: true };
}
