import { notFound } from 'next/navigation';
import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { ProjectDoc } from '@/schemas';
import { ProjectQuoteDoc } from '@/schemas/projects/subdocuments';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function (
  project: ProjectDoc | null,
  quote?: ProjectQuoteDoc | null | undefined,
) {
  'use server';
  const { user, account, isSystemAdmin } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not edit project quotes.');

  const sameAccount = account._id.equals(project?.account) || isSystemAdmin;

  const canEdit = await can(user, 'edit_quote_on_project', sameAccount, !project?.readOnly);
  if (!canEdit) throw new ServerError('Forbidden: you can not edit project quotes.');

  const canDelete = await can(user, 'edit_quote_on_project');

  if (!project?._id || !quote?._id) notFound();

  return { user, account, project, quote, canEdit, canDelete };
});
