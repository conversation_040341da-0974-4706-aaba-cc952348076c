include:
  - ./replica-set.yml

services:
  dev:
    depends_on:
      - init
    image: node:20-bookworm
    restart: on-failure
    working_dir: /app
    ports:
      - 3000:3000
    volumes:
      - ../:/app
    environment:
      TZ: utc
      NODE_ENV: development
      MONGODB_URI: ${MONGODB_URI:-***********************************************************************************************}
    entrypoint: ['/bin/sh', '-c', 'npm ci --include=dev && npm run dev']
