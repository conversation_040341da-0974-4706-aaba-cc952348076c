import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';

import { getAutomationModel } from '@/schemas';
import { validateRequest } from '@/server/auth';
import { can } from '@/lib/capabilities';
import { logger } from '@/lib/logger';

interface ToggleRequestBody {
  automationId: string;
  isActive: boolean;
}

export async function POST(request: NextRequest) {
  try {
    const { user, account } = await validateRequest();

    if (!user || !account) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const canEdit = await can(user, 'edit_automation');
    if (!canEdit) {
      return NextResponse.json({ error: 'You do not have permission to edit automations' }, { status: 403 });
    }

    const body: ToggleRequestBody = await request.json();
    const { automationId, isActive } = body;

    if (!automationId || typeof isActive !== 'boolean') {
      return NextResponse.json({ error: 'Invalid request data' }, { status: 400 });
    }

    if (!mongoose.Types.ObjectId.isValid(automationId)) {
      return NextResponse.json({ error: 'Invalid automation ID' }, { status: 400 });
    }

    const automationModel = await getAutomationModel();
    const automation = await automationModel.findOne({
      _id: new mongoose.Types.ObjectId(automationId),
      account: account._id,
    });

    if (!automation) {
      return NextResponse.json({ error: 'Automation not found' }, { status: 404 });
    }

    // Use updateOne with validation disabled to avoid validation issues with other fields
    await automationModel.updateOne(
      { _id: new mongoose.Types.ObjectId(automationId), account: account._id },
      { $set: { isActive } },
      { runValidators: false },
    );

    return NextResponse.json({
      success: true,
      message: `Automation ${isActive ? 'activated' : 'deactivated'} successfully`,
    });
  } catch (error) {
    logger.error('Toggle automation error:', error);

    if (error instanceof SyntaxError) {
      return NextResponse.json({ error: 'Invalid JSON in request body' }, { status: 400 });
    }

    if (error instanceof mongoose.Error.ValidationError) {
      return NextResponse.json({ error: 'Validation error' }, { status: 400 });
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
