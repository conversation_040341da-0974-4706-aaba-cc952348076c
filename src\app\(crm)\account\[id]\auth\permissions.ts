import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function (id: string) {
  'use server';
  const { user, account, isSystemAdmin } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not edit account authentication.');

  let canEdit = await can(user, 'edit_account_auth');
  canEdit = (account._id.equals(id) && canEdit) || isSystemAdmin;
  if (!canEdit) throw new ServerError('Forbidden: you can not edit account authentication.');

  return { canEdit, user, account };
});
