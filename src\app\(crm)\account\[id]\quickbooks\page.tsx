import { Metadata, ResolvingMetadata } from 'next';

import { WidgetEdit, WithTests } from '@/lib/account';
import { Field, Form, SecretField } from '@/lib/form';
import { Card, CardContent } from '@/components/ui/card';

import { getData } from './helpers';
import { TestConnection } from './tests';
import { EditPageProps } from './types';

export async function generateMetadata(_: unknown, parent: ResolvingMetadata): Promise<Metadata> {
  const parentMetadata = await parent;

  return {
    title: 'QuickBooks | ' + parentMetadata.title?.absolute,
  };
}

export default async function EditPage({ params }: Readonly<EditPageProps>) {
  const { account, editAction } = await getData(params.id);

  return (
    <Form action={editAction}>
      <WidgetEdit label={account.name} id={account.id}>
        <WithTests canTest={true} tests={<TestConnection />}>
          <Card>
            <CardContent className="pt-4">
              <div className="space-y-4">
                <Field name="clientId" label="Client ID" type="text" value={account.settings?.quickBooks?.clientId} />
                <Field
                  required
                  type="radio"
                  name="environment"
                  label="Environment"
                  value={account.settings?.quickBooks?.environment}
                  options={['sandbox', 'production']}
                />
                <Field
                  name="redirectUri"
                  label="Redirect URI"
                  type="text"
                  value={account.settings?.quickBooks?.redirectUri}
                />
                <SecretField isSet={!!account?.settings?.quickBooks?.clientSecret} label="Edit Secret">
                  <Field autoComplete="off" name="clientSecret" label="Secret" type="password" />
                </SecretField>
              </div>
            </CardContent>
          </Card>
        </WithTests>
      </WidgetEdit>
    </Form>
  );
}
