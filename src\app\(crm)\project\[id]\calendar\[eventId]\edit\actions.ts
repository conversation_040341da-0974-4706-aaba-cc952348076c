import mongoose from 'mongoose';
import { cache } from 'react';
import { redirect, notFound } from 'next/navigation';

import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';
import { ProjectDoc } from '@/schemas';
import { db } from '@/lib/database';
import { getProject } from '@/server/projects/get-project';
import { getCalendarEventModel } from '@/schemas/calendar-event';
import { can } from '@/lib/capabilities';
import { getStartAndEndDates } from '@/lib/calendar/helpers';
import { checkAvailableCrew } from '@/lib/calendar/actions';
import { convertFromUserTz } from '@/lib/timezone';

import { checkIsDelivery, checkIsLabor, checkIsAppointment } from './helpers';

export const validatePermissions = cache(async function () {
  'use server';
  const { user, account } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not edit calendar events.');

  const canScheduleDeliveryEvents = await can(user, 'schedule_delivery_events');
  const canScheduleLaborEvents = await can(user, 'schedule_labor_events');

  return {
    user,
    account,
    canScheduleDeliveryEvents,
    canScheduleLaborEvents,
  };
});

interface DeleteCalendarEventParams {
  eventId: string;
  project: ProjectDoc;
  type: string;
}

const deleteCalendarEvent = async ({ eventId, project, type }: DeleteCalendarEventParams) => {
  try {
    if (!project) throw new ServerError('Not Found');

    const calendarEvent = await getCalendarEventModel();

    if (checkIsDelivery(type)) {
      project.deliveryEvents.pull(eventId);
    } else if (checkIsLabor(type)) {
      const laborEvent = project.laborEvents.id(eventId);

      project.laborEvents.pull(eventId);
      await calendarEvent.deleteOne({ _id: laborEvent?.event._id });
    } else if (checkIsAppointment(type)) {
      project.appointments.pull(eventId);
    }

    await project.save({ timestamps: false });
  } catch (error) {
    if (error instanceof Error) {
      return { error: error.message };
    }

    return { error: 'Unknown error' };
  }
};

const edit = async (params: { projectId: string; eventId: string; type: string }, _: unknown, formData: FormData) => {
  'use server';

  const { projectId, eventId, type } = params;

  try {
    await db();

    const { user } = await validatePermissions();
    const { data: project } = await getProject(projectId);
    if (!project) throw new ServerError('Not Found');

    const userTz = user?.tzoffset ?? 0;

    const { _action } = Object.fromEntries(formData);

    if (_action === 'delete') {
      await deleteCalendarEvent({ eventId, project, type });
    } else if (checkIsDelivery(type)) {
      await editDeliveryEvent({ project, eventId, userId: user._id, userTz }, formData);
    } else if (checkIsLabor(type)) {
      await editLaborEvent({ project, eventId, userId: user._id, userTz }, formData);
    } else if (checkIsAppointment(type)) {
      await editAppointment({ project, eventId, userId: user._id, userTz }, formData);
    }
  } catch (error) {
    if (error instanceof Error) {
      return { error: error.message };
    }

    return { error: 'Unknown error' };
  }
  redirect(`/project/${projectId}`);
};

export const getData = cache(async function (projectId: string, eventId: string) {
  'use server';
  const { user, canScheduleDeliveryEvents, canScheduleLaborEvents } = await validatePermissions();
  const { data: project } = await getProject(projectId);

  if (!user) notFound();
  if (!project) notFound();

  const deliveryEvent = project.deliveryEvents.find((event) => event._id.toString() === eventId);
  const laborEvent = project.laborEvents.find((event) => event._id.toString() === eventId);
  const appointment = project.appointments.find((event) => event._id.toString() === eventId);

  const type = deliveryEvent?.type || laborEvent?.type || appointment?.type;

  if (!type) notFound();

  const canScheduleAppointments =
    (await can(user, 'schedule_appointments')) || appointment?.createdBy.toString() === user._id.toString();

  if (checkIsAppointment(type) && !canScheduleAppointments) {
    throw new ServerError('Forbidden: you can not edit calendar events.');
  }
  if (checkIsDelivery(type) && !canScheduleDeliveryEvents) {
    throw new ServerError('Forbidden: you can not edit calendar events.');
  }
  if (checkIsLabor(type) && !canScheduleLaborEvents) {
    throw new ServerError('Forbidden: you can not edit calendar events.');
  }

  const editAction = edit.bind(null, {
    projectId,
    eventId,
    type,
  });

  return { deliveryEvent, laborEvent, appointment, editAction, userTz: user?.tzoffset ?? 0 };
});

interface EditEventParams {
  project: ProjectDoc;
  eventId: string;
  userId: mongoose.Types.ObjectId;
  userTz: number;
}

const editDeliveryEvent = async (params: EditEventParams, formData: FormData) => {
  const { project, eventId, userId, userTz } = params;
  const { title, start, time, procurementMethod, deliveryMethod, description } = Object.fromEntries(formData);

  const deliveryEvent = project.deliveryEvents.id(eventId);
  if (!deliveryEvent) throw new Error('Delivery event not found');

  try {
    deliveryEvent.set({
      title,
      start: convertFromUserTz(new Date(start + ''), userTz),
      time,
      procurementMethod,
      deliveryMethod,
      description,
      modified: new Date(),
      modifiedBy: userId,
    });

    await deliveryEvent.validate();

    await project.save({ timestamps: false });
  } catch (error) {
    if (error instanceof Error) {
      return { error: error.message };
    }

    return { error: 'Unknown error' };
  }
};

const editLaborEvent = async (params: EditEventParams, formData: FormData) => {
  const { project, eventId, userId, userTz } = params;

  const calendarEventModel = await getCalendarEventModel();
  const { title, description } = Object.fromEntries(formData);
  const crew = formData.get('crew');
  if (!crew) {
    throw new Error('Labor must be assigned to a crew');
  }

  const laborEvent = project.laborEvents.id(eventId);
  const calendarEvent = await calendarEventModel.findOne({ _id: laborEvent?.event._id });
  if (!laborEvent || !calendarEvent) throw new Error('Event not found');

  const { startDate, endDate } = getStartAndEndDates(
    convertFromUserTz(new Date(formData.get('start') + ''), 0),
    convertFromUserTz(new Date(formData.get('end') + ''), 0),
    userTz,
  );
  await checkAvailableCrew(crew.toString(), startDate, endDate, calendarEvent._id.toString());

  try {
    calendarEvent.set({
      crew,
      start: startDate,
      end: endDate,
      title,
      description,
      modified: new Date(),
      modifiedBy: userId,
    });

    laborEvent.set({
      title,
      description,
      crew,
      modified: new Date(),
      modifiedBy: userId,
    });

    await laborEvent.validate();

    await project.save({ timestamps: false });
    await calendarEvent.save();
  } catch (error) {
    if (error instanceof Error) {
      return { error: error.message };
    }

    return { error: 'Unknown error' };
  }
};

const editAppointment = async (params: EditEventParams, formData: FormData) => {
  const { project, eventId, userId, userTz } = params;
  const { title, start, end, assignedTo, description } = Object.fromEntries(formData);

  if (!assignedTo) {
    throw new Error('Appointment must be assigned to a user');
  }

  const appointment = project.appointments.id(eventId);
  if (!appointment) throw new Error('Appointment not found');

  try {
    appointment.set({
      title,
      start: convertFromUserTz(new Date(start + ''), userTz),
      end: convertFromUserTz(new Date(end + ''), userTz),
      assignedTo,
      description,
      modified: new Date(),
      modifiedBy: userId,
    });

    await appointment.validate();

    await project.save({ timestamps: false });
  } catch (error) {
    if (error instanceof Error) {
      return { error: error.message };
    }

    return { error: 'Unknown error' };
  }
};
