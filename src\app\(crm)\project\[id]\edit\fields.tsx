'use client';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Browser<PERSON><PERSON>ch, type BrowserSelectOption } from '@/lib/form';
import { SCHEMAS } from '@/lib/schemas';

export function LocationField({ value }: Readonly<{ value?: BrowserSelectOption }>) {
  return (
    <BrowserField
      headers={{ name: 'Name' }}
      name="location"
      label="Location"
      required
      value={value}
      callback={BrowserSearch({ model: SCHEMAS.LOCATION })}
    />
  );
}
