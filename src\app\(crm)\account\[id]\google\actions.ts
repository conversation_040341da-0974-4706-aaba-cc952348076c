import mongoose from 'mongoose';
import { revalidatePath } from 'next/cache';

import { db } from '@/lib';
import type { ActionResult } from '@/lib/form';
import { assertString } from '@/lib/validators';
import { getAccountModel } from '@/schemas';

import { validatePermissions } from './permissions';

export async function edit(id: string, _: unknown, formData: FormData): Promise<ActionResult> {
  'use server';
  try {
    await db();
    await validatePermissions(id);

    const accountModel = await getAccountModel();
    const account = await accountModel.findOne({ _id: new mongoose.mongo.ObjectId(id) });
    if (!account) throw new Error(`Account ${id} does not exist`);

    const apikey = formData.get('apikey');
    if (assertString(apikey)) account.set('settings.google.apikey', apikey);

    await account.save();
  } catch (e) {
    if (e instanceof Error) return { error: e.message };

    return { error: 'Unknown error' };
  }

  revalidatePath(`/account/${id}`);
  return { error: null };
}
