'use client';

import { Edit } from 'lucide-react';
import { useState } from 'react';
import { useFormStatus } from 'react-dom';

import { Spinner } from '@/lib/form';
import { Button } from '@/components/ui/button';

export function RenameButton({
  projectId,
  fileId,
  filename,
}: Readonly<{
  projectId: string;
  fileId: string;
  filename: string;
}>) {
  const [isRenaming, setIsRenaming] = useState(false);
  const { pending } = useFormStatus();

  if (isRenaming) {
    return (
      <div className="flex items-center">
        <input
          type="text"
          name="newFilename"
          defaultValue={filename}
          className="bg-slate-700 text-white rounded-md px-2 py-1 text-sm mr-2 w-40"
          autoFocus
          onKeyDown={(e) => {
            if (e.key === 'Escape') {
              setIsRenaming(false);
            }
          }}
        />
        <input type="hidden" value={projectId} name="projectId" readOnly />
        <input type="hidden" value={fileId} name="fileId" readOnly />
        <Button
          type="submit"
          disabled={pending}
          className="bg-green-600 flex w-6 h-6 justify-center items-center cursor-pointer rounded-lg mr-1"
        >
          {pending ? <Spinner height="h-4" /> : <Edit className="w-4 h-4" />}
        </Button>
        <Button
          type="button"
          onClick={() => setIsRenaming(false)}
          className="bg-gray-600 flex w-6 h-6 justify-center items-center cursor-pointer rounded-lg"
        >
          ✕
        </Button>
      </div>
    );
  }

  return (
    <Button
      type="button"
      onClick={() => setIsRenaming(true)}
      className="bg-blue-600 flex w-6 h-6 justify-center items-center cursor-pointer rounded-lg mr-1"
    >
      <Edit className="w-4 h-4" />
    </Button>
  );
}
