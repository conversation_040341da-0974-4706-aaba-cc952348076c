import { notFound } from 'next/navigation';
import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { CustomerDoc } from '@/schemas';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function (customer: CustomerDoc | null) {
  'use server';
  const { user, account, isSystemAdmin } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not view customers.');

  const sameAccount = account._id.equals(customer?.account) || isSystemAdmin;

  const canView = await can(user, 'view_customer', sameAccount);
  if (!canView) throw new ServerError('Forbidden: you can not view customers.');

  const canEdit = await can(user, 'edit_customer', sameAccount);

  if (!customer?._id) notFound();

  return { canView, canEdit, user, account, customer };
});
