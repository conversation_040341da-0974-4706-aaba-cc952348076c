import Link from 'next/link';

import { Form } from '@/lib/form';
import { formatDate } from '@/lib/formatting';
import { assertProject } from '@/lib/project';
import { ListItem } from '@/lib/widget';
import { type ProjectDoc } from '@/schemas';

import { DeleteButton } from './delete-button';
import { assertUserDoc, getData, getCommentReplies } from './helpers';
import { ReplyForm } from './reply-form';
import { ToggleReplies } from './toggle-replies';

interface CommentProps {
  comment: ProjectDoc['comments'][number];
  isReply?: boolean;
}

export async function Comment({ comment, isReply = false }: Readonly<CommentProps>) {
  const project = comment.$parent();
  if (!assertProject(project)) return null;

  const {
    user: currentUser,
    canDeleteComment,
    canViewUsers,
    canPostComment,
    deleteComment,
  } = await getData(project.id);
  const user = comment.documents?.user;
  const replies = getCommentReplies(project.comments, comment.id);

  let userRender: React.ReactNode = null;

  if (assertUserDoc(user) && canViewUsers) {
    userRender = (
      <div>
        <Link className="underline" href={`/user/${user.id}`}>
          {user.name}
        </Link>
      </div>
    );
  } else if (user) {
    userRender = <div>{user.name}</div>;
  }

  // Define interface for reply object structure
  interface ReplyObject {
    id: string;
    value: string;
    created: Date;
    documents: {
      user: {
        name: string;
        id: string;
      };
    };
    projectId: string;
    parentComment?: string;
    childReplies: ReplyObject[];
  }

  // Build a tree of replies for nested comments
  const repliesMap = new Map<string, ReplyObject>();
  const topLevelReplies: ReplyObject[] = [];

  // First pass: collect all replies and map them by ID
  replies.forEach((reply) => {
    const replyId = reply.id.toString();
    repliesMap.set(replyId, {
      id: replyId,
      value: reply.value,
      created: reply.created,
      documents: {
        user: {
          name: reply.documents?.user?.name || 'Unknown',
          id: assertUserDoc(reply.documents?.user) ? reply.documents.user.id : '',
        },
      },
      projectId: project.id.toString(),
      parentComment: reply.parentComment?.toString(),
      childReplies: [],
    });
  });

  // Get all project comments to ensure we don't miss any
  const allProjectComments = project.comments || [];

  // Second pass: organize replies into a hierarchical structure
  replies.forEach((reply) => {
    const replyId = reply.id.toString();
    const replyObj = repliesMap.get(replyId);

    if (!replyObj) return; // Skip if not found in map

    // Find any replies that are children of this reply (including ones that might not be in our initial replies array)
    const childReplies = allProjectComments.filter((r) => r.parentComment?.toString() === replyId);

    // Add child replies to this reply
    if (childReplies.length > 0) {
      replyObj.childReplies = childReplies
        .map((child) => {
          const childId = child.id.toString();
          // If the child isn't in our map yet, add it
          if (!repliesMap.has(childId)) {
            repliesMap.set(childId, {
              id: childId,
              value: child.value,
              created: child.created,
              documents: {
                user: {
                  name: child.documents?.user?.name || 'Unknown',
                  id: assertUserDoc(child.documents?.user) ? child.documents.user.id : '',
                },
              },
              projectId: project.id.toString(),
              parentComment: child.parentComment?.toString(),
              childReplies: [],
            });
          }
          return repliesMap.get(childId);
        })
        .filter((child): child is ReplyObject => child !== undefined);
    }

    // If this is a direct reply to the main comment, add it to top level replies
    const commentId = comment.id.toString();
    if (reply.parentComment?.toString() === commentId) {
      topLevelReplies.push(replyObj);
    }
  });

  // Use top level replies for the toggle component - now properly typed with ReplyObject[]
  const repliesForClient: ReplyObject[] = topLevelReplies;

  return (
    <>
      <ListItem className={isReply ? 'ml-8 pl-4' : ''}>
        <div className="flex gap-3 items-center">
          <div className="grow flex flex-col gap-3 projecting-tight">
            <div className="font-bold">{comment.value}</div>

            <div className="flex gap-3 text-xs">
              {userRender}
              <div>{formatDate(comment.created, true, currentUser.tzoffset)}</div>
            </div>
          </div>
          <div className="flex gap-2">
            {canPostComment && (
              <div style={{ minWidth: '60px' }}>
                <ReplyForm projectId={project.id} parentCommentId={comment.id} />
              </div>
            )}
            {!!canDeleteComment && (
              <Form action={deleteComment}>
                <input type="hidden" name="id" value={project.id} readOnly />
                <input type="hidden" name="comment_id" value={comment.id} readOnly />
                <DeleteButton />
              </Form>
            )}
          </div>
        </div>
      </ListItem>

      {/* Show replies toggle if there are replies */}
      {replies.length > 0 && <ToggleReplies replies={repliesForClient} />}
    </>
  );
}
