import { notFound } from 'next/navigation';
import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { DeadLeadReasonDoc } from '@/schemas';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function (deadLeadReason: DeadLeadReasonDoc | null) {
  'use server';
  const { user, account, isSystemAdmin } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not edit dead lead reasons.');

  const sameAccount = account._id.equals(deadLeadReason?.account) || isSystemAdmin;

  const canEdit = await can(user, 'edit_deadleadreason', sameAccount);
  if (!canEdit) throw new ServerError('Forbidden: you can not edit dead lead reasons.');

  const canDelete = await can(user, 'delete_deadleadreason', sameAccount);

  if (!deadLeadReason?._id) notFound();

  return { canEdit, canDelete, user, account, deadLeadReason };
});
