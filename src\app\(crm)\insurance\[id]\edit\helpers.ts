import { cache } from 'react';

import { getInsurance } from '@/server/insurance';

import { edit } from './actions';
import { validatePermissions } from './permissions';

export const getData = cache(async function (id: string) {
  'use server';
  const { data } = await getInsurance(id);
  const { canEdit, canDelete, user, account, insurance } = await validatePermissions(data);
  const editAction = edit.bind(null, id, canDelete);

  return { canEdit, canDelete, user, account, insurance, editAction };
});
