import { notFound } from 'next/navigation';
import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { InsuranceCompanyDoc } from '@/schemas';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function (insurance: InsuranceCompanyDoc | null) {
  'use server';
  const { user, account, isSystemAdmin } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not edit insurance companies.');

  const sameAccount = account._id.equals(insurance?.account) || isSystemAdmin;

  const canEdit = await can(user, 'edit_insurancecompany', sameAccount);
  if (!canEdit) throw new ServerError('Forbidden: you can not edit insurance companies.');

  const canDelete = await can(user, 'delete_insurancecompany', sameAccount);

  if (!insurance?._id) notFound();

  return { canEdit, canDelete, user, account, insurance };
});
