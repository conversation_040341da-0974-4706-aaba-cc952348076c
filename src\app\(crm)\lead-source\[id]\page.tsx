import Link from 'next/link';

import { DisplayData } from '@/lib/view';
import { EditIcon, Widget } from '@/lib/widget';
import { ActionFooter } from '@/lib/nav';

import { getData } from './helpers';
import { ViewLeadSourceProps } from './types';

export default async function ViewLeadSource({ params }: ViewLeadSourceProps) {
  const { leadSource, canEdit, account } = await getData(params.id);

  const editLink = `/lead-source/${leadSource._id}/edit`;

  return (
    <div className="space-y-4">
      <Widget label={leadSource.name} action={canEdit && <EditIcon href={editLink} />}>
        <DisplayData label="Name" value={leadSource.name} />
      </Widget>
      <ActionFooter
        left={<Link href={`/account/${account._id.toString()}/custom-fields`}>Back to Dead Lead Reasons</Link>}
      />
    </div>
  );
}
