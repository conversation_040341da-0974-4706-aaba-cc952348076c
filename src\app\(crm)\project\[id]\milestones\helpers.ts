import { cache } from 'react';

import { getProject } from '@/server/projects';
import { getDeadLeadReasons } from '@/server/dead-lead-reason';
import { makeClientSafe } from '@/server';
import { Account, Project, User } from '@/schemas';

import { validatePermissions } from './permissions';
import { advanceMilestone, recedeMilestone, deadReason, makeDead, makeAlive } from './actions';
import { GetDataReturn } from './types';

export const getData = cache(async function (id: string): Promise<GetDataReturn> {
  'use server';
  const { data } = await getProject(id);
  const { data: deadLeadReasons } = await getDeadLeadReasons(data!.account._id.toString()!);

  const props = await validatePermissions(data, deadLeadReasons);
  const advanceMilestoneAction = advanceMilestone.bind(null, id);
  const recedeMilestoneAction = recedeMilestone.bind(null, id);
  const deadReasonAction = deadReason.bind(null, id);
  const makeDeadAction = makeDead.bind(null, id);
  const makeAliveAction = makeAlive.bind(null, id);

  // Convert Mongoose objects to plain JavaScript objects to avoid warnings
  const safeProps = makeClientSafe<{
    project: Project;
    user: User;
    account: Account;
    canEdit: boolean;
  }>(props);
  const safeDeadLeadReasons = makeClientSafe<{ name: string; _id?: string }[]>(deadLeadReasons);

  // Ensure we have all the required properties for the return type
  return {
    project: safeProps.project,
    user: safeProps.user,
    account: safeProps.account,
    canEdit: safeProps.canEdit,
    deadLeadReasons: safeDeadLeadReasons,
    advanceMilestoneAction,
    recedeMilestoneAction,
    deadReasonAction,
    makeDeadAction,
    makeAliveAction,
  };
});
