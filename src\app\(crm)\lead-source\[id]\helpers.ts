import { cache } from 'react';

import { getLeadSource } from '@/server/lead-source';

import { validatePermissions } from './permissions';

export const getData = cache(async function (id: string) {
  'use server';
  const { data } = await getLeadSource(id);
  const { canEdit, canView, user, account, leadSource } = await validatePermissions(data);

  return { canEdit, canView, user, account, leadSource };
});
