import { AccountLogo, WidgetEdit } from '@/lib/account';
import { Field, Form } from '@/lib/form';
import { Card, CardContent } from '@/components/ui/card';

import { getData } from './helpers';
import { Props } from './types';

export default async function EditPage({ params }: Readonly<Props>) {
  const { account, editAction, canDelete } = await getData(params.id);

  return (
    <Form action={editAction}>
      <WidgetEdit label={account.name} id={account.id} canDelete={canDelete}>
        <Card>
          <CardContent className="pt-4">
            <div className="grid grid-cols-1 gap-4">
              <Field name="name" label="Name" value={account.name} />
              <Field name="prefix" label="Prefix" value={account.prefix} />
              <Field name="domain" label="URL" value={account.domain} readOnly />

              <div className="flex gap-6">
                <div className="grow">
                  <Field name="logo" label="Logo" type="file" accept=".jpg, .jpeg, .png, .webm" />
                </div>

                {account?.settings?.logo && (
                  <div>
                    <AccountLogo
                      id={account.id}
                      className="w-24 h-24 object-cover"
                      alt={account.documents?.settings?.logo?.filename || ''}
                    />
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </WidgetEdit>
    </Form>
  );
}
