'use server';

import mongoose from 'mongoose';
import { redirect } from 'next/navigation';

import { db } from '@/lib';
import { ActionResult } from '@/lib/form';
import { getTemplateFromFormData } from '@/lib/quote/mutate/save';
import { getProjectModel } from '@/schemas';
import { getProjectQuoteModel } from '@/schemas/projects/subdocuments';

import { getData } from './helpers';

export async function create(projectId: string, _: unknown, formData: FormData): Promise<ActionResult> {
  'use server';
  try {
    await db();
    await getData(projectId);

    const projectModel = await getProjectModel();
    const project = await projectModel.findOne({ _id: new mongoose.mongo.ObjectId(projectId) });
    if (!project) throw new Error('No project found');

    const projectQuoteModel = await getProjectQuoteModel();
    const template = new projectQuoteModel({ _id: new mongoose.mongo.ObjectId() });
    await getTemplateFromFormData(formData, template);

    project.quotes.push(template);
    project.markModified('quotes');

    await project.save({ timestamps: false });
  } catch (error) {
    if (error instanceof Error) {
      return { error: error.message };
    }

    return { error: 'Unknown error' };
  }

  redirect(`/project/${projectId}/quote-flow`);
}
