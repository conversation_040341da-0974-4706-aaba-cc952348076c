import { db } from '@/lib';
import { getCustomerModel } from '@/schemas';
import { CustomerDoc } from '@/schemas/customers/types';

/**
 * Find customers with exact phone match
 */
export async function findExactPhoneMatch({
  phone,
  accountId,
}: {
  phone: string;
  accountId?: string;
}): Promise<CustomerDoc | null> {
  await db();
  const customerModel = await getCustomerModel();

  // Format phone number to remove non-digits for consistent checking
  const formattedPhone = phone.replace(/[^0-9]/g, '');

  // Build the query to find exactly matching phone
  const query: { phoneNumbers: string; account?: string } = { phoneNumbers: formattedPhone };

  // Add account restriction if provided
  if (accountId) {
    query.account = accountId;
  }

  return customerModel.findOne(query).exec();
}

/**
 * Find similar customers based on name, phone, or email
 * This helps identify potential duplicates even when phone numbers are different
 */
export async function findSimilarCustomers({
  firstName,
  lastName,
  phone,
  email,
  accountId,
}: {
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  accountId?: string;
}): Promise<CustomerDoc[]> {
  await db();
  const customerModel = await getCustomerModel();

  // Format phone number to remove non-digits for consistent checking
  const formattedPhone = phone.replace(/[^0-9]/g, '');

  // Build the query for similar (not exact) matches
  const query: {
    $or: Array<
      | { firstName: { $regex: RegExp }; lastName: { $regex: RegExp } }
      | { emailAddresses?: string }
      | { phoneNumbers?: { $regex: RegExp } }
    >;
    account?: string;
    phoneNumbers?: { $ne: string };
  } = {
    $or: [
      // Similar name - fuzzy match on first and last name
      {
        firstName: { $regex: new RegExp(`^${firstName}`, 'i') },
        lastName: { $regex: new RegExp(`^${lastName}`, 'i') },
      },
      // Similar name with swapped first/last names (in case of entry errors)
      {
        firstName: { $regex: new RegExp(`^${lastName}`, 'i') },
        lastName: { $regex: new RegExp(`^${firstName}`, 'i') },
      },
    ],
  };

  // Add email match if provided
  if (email) {
    query.$or.push({ emailAddresses: email.toLowerCase() });
  }

  // Add similar phone number match (starts with same digits)
  if (formattedPhone && formattedPhone.length > 5) {
    // Use the first 6 digits for a partial match
    const partialPhone = formattedPhone.substring(0, 6);
    query.$or.push({
      phoneNumbers: { $regex: new RegExp(`^${partialPhone}`) },
    });
  }

  // Add account restriction if provided
  if (accountId) {
    query.account = accountId;
  }

  // Exclude exact phone matches since we'll show those separately
  query.phoneNumbers = { $ne: formattedPhone };

  // Execute the query with a limit
  return customerModel.find(query).limit(5).exec();
}

/**
 * Formats a customer's information for display
 */
export function formatCustomerForDisplay(customer: CustomerDoc) {
  if (!customer) {
    return {
      _id: null,
      firstName: '',
      lastName: '',
      name: 'Unknown Customer',
      phoneNumbers: [],
      emailAddresses: [],
    };
  }

  return {
    // Convert ObjectId to string to avoid serialization issues
    _id: customer._id ? customer._id.toString() : null,
    firstName: customer.firstName || '',
    lastName: customer.lastName || '',
    name: customer.name || `${customer.firstName || ''} ${customer.lastName || ''}`.trim() || 'Unnamed Customer',
    phoneNumbers: customer.phoneNumbers || [],
    emailAddresses: customer.emailAddresses || [],
  };
}
