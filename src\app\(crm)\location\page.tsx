import Link from 'next/link';

import { formatDate } from '@/lib/formatting';
import { SCHEMAS } from '@/lib/schemas';
import { ListScreen } from '@/lib/screen';
import { Cell } from '@/lib/table';
import type { LocationDoc } from '@/schemas';
import { validateRequest } from '@/server/auth';

import { LocationListProps } from './types';

export default async function ListLocations({ searchParams }: Readonly<LocationListProps>) {
  const { user } = await validateRequest();

  return (
    <ListScreen<LocationDoc>
      {...{ searchParams }}
      model={SCHEMAS.LOCATION}
      label="Locations"
      heading={
        <>
          <Cell>Name</Cell>
          <Cell>Created</Cell>
        </>
      }
      body={async (item) => {
        return (
          <>
            <Cell scope="row">
              <Link href={`/location/${item.id}`} className="hover:underline">
                {item.name}
              </Link>
            </Cell>

            <Cell>{formatDate(item.created, true, user?.tzoffset)}</Cell>
          </>
        );
      }}
    />
  );
}
