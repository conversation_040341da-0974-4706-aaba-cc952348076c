import mongoose from 'mongoose';

import { db } from '@/lib/database';
import { SCHEMAS } from '@/lib/schemas';

import { AutomationSchema } from './schema';
import { Automation } from './types';

const DOC_NAME = SCHEMAS.AUTOMATION;

export async function getAutomationModel() {
  'use server';
  await db();

  if (mongoose.models[DOC_NAME]) return mongoose.model<Automation>(DOC_NAME);

  return mongoose.model<Automation>(DOC_NAME, AutomationSchema.schema);
}
