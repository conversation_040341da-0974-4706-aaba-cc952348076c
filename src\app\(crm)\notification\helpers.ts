import { cache } from 'react';

import type { Notification } from '@/schemas';
import { paginateModel, SCHEMAS } from '@/server';

import { validatePermissions } from './permissions';

export const getData = cache(async function (page?: string) {
  'use server';
  const { user } = await validatePermissions();
  const props = await paginateModel<Notification>({
    model: SCHEMAS.NOTIFICATION,
    page,
    filter: { user: user._id },
  });

  return { ...props, user };
});
