import Link from 'next/link';
import { Metadata, ResolvingMetadata } from 'next/types';

import { Field, Form, FormButton, Warning } from '@/lib/form';
import { convertDbRecordToSelectOption } from '@/lib/form/convert-db-record-to-select-option';
import { ActionFooter } from '@/lib/nav';
import { Widget } from '@/lib/widget';

import { InsuranceCompanyField } from './fields';
import { getData } from './helpers';
import { ProjectInsuranceProps } from './types';

export async function generateMetadata(_: unknown, parent: ResolvingMetadata): Promise<Metadata> {
  const parentMetadata = await parent;

  return {
    title: 'Insurance | ' + parentMetadata.title?.absolute,
  };
}

export default async function ProjectInsurance({ params }: Readonly<ProjectInsuranceProps>) {
  const { project, editAction } = await getData(params.id);
  const insuranceDocuments = project.insurance?.documents;
  const notAssigned = !project.assignedTo;

  return (
    <>
      {notAssigned && (
        <Warning className="mb-6">
          Please assign a Project Manager or Account Admin to the project before entering insurance information.
        </Warning>
      )}
      <Form action={editAction} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Widget label="Insurance Information" className="grid grid-cols-1 gap-4">
            <InsuranceCompanyField value={await convertDbRecordToSelectOption(insuranceDocuments?.company)} />
            <Field value={project.insurance?.claim?.filed} type="boolean" name="claimFiled" label="Claim Filed" />
            <Field value={project.insurance?.claim?.number} name="claimNumber" label="Claim Number" />
            <Field value={project.damageLocation} name="damageLocation" label="Damage Location" />
            <Field value={project.insurance?.dateOfLoss} name="dateOfLoss" type="date" label="Date of Loss" />
            <Field value={project.insurance?.hasPaperwork} type="boolean" name="hasPaperwork" label="Has Paperwork" />
          </Widget>

          <Widget label="Adjuster Information" className="grid grid-cols-1 gap-4">
            <Field value={project.adjuster?.name} name="name" label="Adjuster Name" />
            <Field value={project.adjuster?.phone} type="tel" name="phone" label="Phone" />
            <Field value={project.adjuster?.type} name="type" label="Type" />
            <Field value={project.adjuster?.email} type="email" name="email" label="Email" />
            <Field
              value={project.adjuster?.metWithAdjuster}
              type="boolean"
              name="metWithAdjuster"
              label="Met With Adjuster"
            />
            <Field value={project.adjuster?.approved} type="boolean" name="approved" label="Claim Approved" />
            <Field value={project.adjuster?.fax} type="tel" name="fax" label="Fax" />
          </Widget>
        </div>

        <ActionFooter
          left={<Link href={`/project/${project._id}`}>Back to Project</Link>}
          right={
            <>
              <Link href={`/project/${project._id}`}>Cancel</Link>
              <FormButton>Save</FormButton>
            </>
          }
        />
      </Form>
    </>
  );
}
