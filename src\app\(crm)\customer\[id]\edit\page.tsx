import { Metadata, ResolvingMetadata } from 'next';
import Link from 'next/link';

import { Action, Field, Form, FormButton } from '@/lib/form';
import { ActionFooter } from '@/lib/nav';
import { Widget } from '@/lib/widget';

import { getData } from './helpers';
import { EditPageProps } from './types';

export async function generateMetadata(_: unknown, parent: ResolvingMetadata): Promise<Metadata> {
  const parentMetadata = await parent;

  return {
    title: 'Edit | ' + parentMetadata.title?.absolute,
  };
}

export default async function EditPage({ params }: Readonly<EditPageProps>) {
  const { customer, editAction, canDelete } = await getData(params.id);

  return (
    <Form className="flex flex-col gap-6" action={editAction}>
      <Widget label={customer.name || 'Contact'}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
          <div className="space-y-4">
            <div className="font-bold text-xl">General</div>
            <Field value={customer.firstName} name="firstName" label="First Name" required />
            <Field value={customer.lastName} name="lastName" label="Last Name" required />
            <Field value={customer.email} name="email" label="Email" type="email" />
            <Field value={customer.phone} name="phone" label="Phone" type="tel" required />
          </div>

          <div className="space-y-4">
            <div className="font-bold text-xl">Invoice</div>
            <Field value={customer.invoice.balance.toString()} name="balance" label="Balance" type="number" required />
            <Field value={customer.invoice.deposit.toString()} name="deposit" label="Deposit" type="number" required />
            <Field value={customer.invoice.overdue.toString()} name="overdue" label="Overdue" type="number" required />
            <Field
              value={customer.invoice.unbilled.toString()}
              name="unbilled"
              label="Unbilled"
              type="number"
              required
            />
          </div>
        </div>
      </Widget>

      <ActionFooter
        left={<Link href={`/customer/${params.id}`}>Back to Customer</Link>}
        right={
          <>
            <FormButton>Save</FormButton>
            {canDelete && <Action />}
          </>
        }
      />
    </Form>
  );
}
