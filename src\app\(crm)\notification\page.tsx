import Link from 'next/link';

import { formatDate } from '@/lib/formatting';
import { Pagination } from '@/lib/nav';
import { NoResultsRow } from '@/lib/screen';
import { Body, Cell, Heading, Row, Table } from '@/lib/table';
import { Widget } from '@/lib/widget';

import { getData } from './helpers';
import { NotificationProps } from './type';

export default async function ListNotifications({ searchParams }: Readonly<NotificationProps>) {
  const { data, pageTotal, page, user } = await getData(searchParams.page);

  const hasNoResults = data.length === 0;

  return (
    <Widget label="Notifications" footer={<Pagination total={pageTotal} page={page} />}>
      <Table>
        <Heading>
          <Cell>Message</Cell>
          <Cell>Status</Cell>
          <Cell>Created</Cell>
        </Heading>
        <Body>
          {data.map(async (item) => {
            return (
              <Row key={item.id}>
                <Cell>
                  {!!item.link ? (
                    <Link href={item.link} className="hover:no-underline underline">
                      {item.message}
                    </Link>
                  ) : (
                    item.message
                  )}
                </Cell>

                <Cell>{item.seen ? 'Read' : 'New'}</Cell>

                <Cell>{formatDate(item.created, true, user.tzoffset)}</Cell>
              </Row>
            );
          })}
        </Body>
      </Table>
      {hasNoResults && <NoResultsRow />}
    </Widget>
  );
}
