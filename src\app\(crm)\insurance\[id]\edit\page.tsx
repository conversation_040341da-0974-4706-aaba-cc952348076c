import Link from 'next/link';

import { Action, Field, Form, FormButton } from '@/lib/form';
import { ActionFooter } from '@/lib/nav';
import { Widget } from '@/lib/widget';

import { getData } from './helpers';

interface EditInsurancePageProps {
  params: { id: string };
}

export default async function EditInsurancePage({ params }: EditInsurancePageProps) {
  const { insurance, editAction, canDelete, account } = await getData(params.id);

  return (
    <Form className="flex flex-col gap-6" action={editAction}>
      <Widget label={insurance.name || 'Insurance'} className="flex flex-col gap-4">
        <Field label="Name" name="name" value={insurance.name} required />
      </Widget>

      <ActionFooter
        left={<Link href={`/account/${account._id.toString()}/custom-fields`}>Back to Insurances</Link>}
        right={
          <>
            <FormButton>Save</FormButton>
            {!!canDelete && <Action />}
          </>
        }
      />
    </Form>
  );
}
