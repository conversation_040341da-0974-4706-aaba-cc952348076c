import { Metadata, ResolvingMetadata } from 'next/types';

export async function generateMetadata(_: unknown, parent: ResolvingMetadata): Promise<Metadata> {
  const parentMetadata = await parent;

  return {
    title: 'DeadLeadReason | ' + parentMetadata.title?.absolute,
  };
}

export default function DeadLeadReasonLayout({ children }: Readonly<{ children: React.ReactNode }>) {
  return children;
}
