import { CA<PERSON><PERSON><PERSON>_EVENT_TYPES } from '@/lib/calendar/constants';
import { formatDate } from '@/lib/formatting/date';
export const checkIsDelivery = (type?: string): type is typeof CALENDAR_EVENT_TYPES.DELIVERY => {
  return type === CALENDAR_EVENT_TYPES.DELIVERY;
};
export const checkIsLabor = (type?: string): type is typeof CALENDAR_EVENT_TYPES.LABOR => {
  return type === CALENDAR_EVENT_TYPES.LABOR;
};
export const checkIsAppointment = (type?: string): type is typeof CALENDAR_EVENT_TYPES.APPOINTMENT => {
  return type === CALENDAR_EVENT_TYPES.APPOINTMENT;
};

export const getLaborDate = (start: Date, end: Date, userTz: number) => {
  const startDate = formatDate(start, false, userTz);
  const endDate = formatDate(end, false, userTz);

  if (startDate === endDate) {
    return startDate;
  }

  return `${startDate} - ${endDate}`;
};

export const getDeliveryDate = (start: Date, time: string, userTz: number) => {
  const startDate = formatDate(start?.toString(), false, userTz);

  return `${startDate}, ${time?.toUpperCase()}`;
};

export const getAppointmentDate = (start: Date, end: Date, userTz: number) => {
  const startDate = formatDate(start, true, userTz);
  const endDate = formatDate(end, true, userTz);

  return `${startDate} - ${endDate}`;
};
