import { validateRequest } from '@/server/auth';
import { DisplayData } from '@/lib/view';
import QuickBooksClient from '@/server/quickbooks-client/client';
import { Button } from '@/lib/form';
export async function testAction(): Promise<{ error?: string; success?: boolean }> {
  try {
    const { account } = await validateRequest();

    if (!account) {
      return {
        success: false,
        error: 'Account not found',
      };
    }

    if (!account.settings?.quickBooks?.credentials) {
      return {
        success: false,
        error: 'QuickB<PERSON><PERSON> is not authorized in your account. Please go to settings and authorize it',
      };
    }

    const qb = new QuickBooksClient(
      account._id.toString(),
      account.settings?.quickBooks?.clientId,
      account.settings?.quickBooks?.clientSecret,
      account.settings?.quickBooks?.credentials.access_token,
      account.settings?.quickBooks?.credentials?.refresh_token,
      account.settings?.quickBooks?.redirectUri,
      account.settings?.quickBooks?.credentials.realm_id,
      account.settings?.quickBooks?.environment,
    );

    await qb.getCompanyInfo();

    return {
      success: true,
    };
  } catch (error) {
    if (error instanceof Error) {
      return { success: false, error: error?.message };
    }

    return { success: false, error: 'Internal Server Error' };
  }
}

export async function TestConnection() {
  const test = await testAction();

  if (!test.success) {
    return (
      <>
        <DisplayData label="Connection Error" value={test.error} />;
        <Button href={`/api/oauth2/quickbooks`}>Authorize</Button>
      </>
    );
  }

  return (
    <>
      <DisplayData label="Connection Status" value="Online" />
    </>
  );
}
