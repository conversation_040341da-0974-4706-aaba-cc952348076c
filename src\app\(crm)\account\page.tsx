import Link from 'next/link';

import { SCHEMAS, translateModelToRoute } from '@/lib/schemas';
import { ListScreen } from '@/lib/screen';
import type { AccountDoc } from '@/schemas';
import { TableCell, TableHead } from '@/components/ui/table';

interface ListAccountProps {
  searchParams: {
    page?: string | undefined;
  };
}

export default async function ListAccount({ searchParams }: Readonly<ListAccountProps>) {
  return (
    <ListScreen<AccountDoc>
      {...{ searchParams }}
      model={SCHEMAS.ACCOUNT}
      label="Accounts"
      heading={
        <>
          <TableHead>Name</TableHead>
          <TableHead>Prefix</TableHead>
        </>
      }
      body={async (item) => {
        const viewUrl = translateModelToRoute(SCHEMAS.ACCOUNT, item.id);

        return (
          <>
            <TableCell scope="row">
              <Link href={viewUrl} className="hover:underline">
                {item.name}
              </Link>
            </TableCell>

            <TableCell>{item.prefix}</TableCell>
          </>
        );
      }}
    />
  );
}
