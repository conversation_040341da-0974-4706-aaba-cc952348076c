import { notFound } from 'next/navigation';
import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { LocationDoc } from '@/schemas';
import { ServerError } from '@/server';
import { getAccount } from '@/server/accounts';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function (location: LocationDoc | null) {
  'use server';
  const { user, account, isSystemAdmin } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not edit locations.');

  const { googleKeyIsSet } = await getAccount(account._id);

  const sameAccount = account._id.equals(location?.account) || isSystemAdmin;

  const canEdit = await can(user, 'edit_Location', sameAccount);
  if (!canEdit) throw new ServerError('Forbidden: you can not edit locations.');

  const canDelete = await can(user, 'delete_Location', sameAccount);

  if (!location?._id) notFound();

  return { canEdit, canDelete, user, account, location, canInputCoords: !googleKeyIsSet };
});
