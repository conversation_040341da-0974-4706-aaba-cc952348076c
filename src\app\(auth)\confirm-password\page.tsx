import Link from 'next/link';

import { <PERSON>, Wrapper } from '@/lib/auth';
import { FormButton, Passwords } from '@/lib/form';

import { resetPassword, setPassword } from './actions';
import { getData } from './helpers';
import { Origin } from './origin';

export default async function RestPassword({ searchParams }: Readonly<{ searchParams: { confirm?: string } }>) {
  const { isConfirming } = await getData(searchParams.confirm);

  return (
    <Wrapper title="Confirm Password" action={isConfirming ? setPassword : resetPassword}>
      {isConfirming ? (
        <>
          <Passwords required />
          <input type="hidden" name="_confirm" value={searchParams.confirm} />
        </>
      ) : (
        <Field name="email" label="Email" placeholder="Enter your email" type="email" required autoComplete="email" />
      )}

      <Origin />

      <FormButton>Confirm Password</FormButton>

      <p className="text-center">
        <Link className="text-orange-500 hover:underline cursor-pointer" href="/login">
          login
        </Link>
      </p>
    </Wrapper>
  );
}
