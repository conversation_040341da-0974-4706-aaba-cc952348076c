import { notFound } from 'next/navigation';
import { cache } from 'react';

import { getProject } from '@/server/projects';
import { SIGNATURE_TYPE } from '@/schemas/projects/subdocuments';

import { validatePermissions } from './permissions';
import { sendSignEmailToCustomer } from './actions';

export const getSignatureName = cache(async function _getSignatureName(
  type: SIGNATURE_TYPE,
  projectId: string,
  orderId: string,
) {
  'use server';
  const { project, user, order, canSignAsAdmin } = await getData(projectId, orderId);

  if (!project) return '';

  let name = 'N/A';
  if (type === SIGNATURE_TYPE.CUSTOMER) name = project.documents?.customer?.name || 'N/A';
  if (type === SIGNATURE_TYPE.ADMIN) {
    name = 'Account Admin';
    const existingSignature = order.signatures.find((signature) => signature.type === type);
    if (existingSignature && existingSignature.documents?.createdBy?.name) {
      name = existingSignature.documents.createdBy.name;
    }

    if (canSignAsAdmin) name = user.name;
  }

  if (type === SIGNATURE_TYPE.PM) {
    const existingSignature = order.signatures.find((signature) => signature.type === type);
    if (existingSignature && existingSignature.documents?.createdBy?.name) {
      return existingSignature.documents.createdBy.name;
    }

    if (!project.documents?.assignedTo) {
      return user.name;
    }

    return project.documents?.assignedTo?.name;
  }

  return name;
});

export const getData = cache(async (projectId: string, orderId: string) => {
  'use server';

  const { data } = await getProject(projectId);

  const {
    project,
    user,
    canEdit,
    canInvoice,
    canScheduleDeliveryEvents,
    canScheduleLaborEvents,
    account,
    canSignAsAdmin,
    canSignAsPm,
    canSignWithCustomerInperson,
    canViewSignatures,
  } = await validatePermissions(data);

  const order = project.orders.find((o) => o.id === orderId);
  const signatures = order?.signatures;

  const existingSignature = signatures?.find((sign) => sign.type === SIGNATURE_TYPE.CUSTOMER);
  const customerSigned = !!existingSignature;

  if (!order) notFound();

  const deliveryEvents = project.deliveryEvents.filter((event) => event.orderId.toString() === orderId);
  const laborEvents = project.laborEvents.filter((event) => event.orderId.toString() === orderId);

  const sendSignEmailToCustomerAction = sendSignEmailToCustomer.bind(null, projectId, orderId);

  return {
    account,
    project,
    order,
    deliveryEvents,
    laborEvents,
    user,
    canEdit,
    canInvoice,
    canScheduleDeliveryEvents,
    canScheduleLaborEvents,
    canSignAsAdmin,
    canSignAsPm,
    canSignWithCustomerInperson,
    canViewSignatures,
    customerSigned,
    sendSignEmailToCustomerAction,
  };
});
