import { generateId } from 'lucia';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import nodemailer from 'nodemailer';

import { db } from '@/lib';
import { type ActionResult } from '@/lib/auth';
import { assertString, validateEmail } from '@/lib/validators';
import { TOKEN_TYPE, getSessionModel } from '@/schemas/sessions';
import { getUserModel } from '@/schemas/users';
import { hashPassword, luciaSession } from '@/server';
import { INVITE_STATUS } from '@/schemas/users/enums';
// import { getAccountFromUrl } from '@/server/accounts';
import { getSystemModel } from '@/schemas';

export async function setPassword(_: unknown, formData: FormData): Promise<ActionResult> {
  'use server';

  const confirm = formData.get('_confirm');
  if (assertString(confirm)) {
    try {
      await db();

      const userModel = await getUserModel();
      const sessionModel = await getSessionModel();

      const existingUser = await userModel.findOne({ confirmToken: confirm });

      if (existingUser?._id) {
        const password = formData.get('password');
        const confirmPassword = formData.get('confirm-password');
        const hashedPassword = await hashPassword(password, confirmPassword);

        existingUser.password = hashedPassword;
        existingUser.verified = true;
        existingUser.confirmToken = '';
        existingUser.inviteStatus = INVITE_STATUS.ACCEPTED;

        await Promise.all([existingUser.save(), sessionModel.deleteMany({ user_id: existingUser._id })]);

        const session = await luciaSession.createSession(existingUser._id, { type: TOKEN_TYPE.COOKIE });
        const sessionCookie = luciaSession.createSessionCookie(session.id);

        cookies().set(sessionCookie.name, sessionCookie.value, sessionCookie.attributes);

        redirect('/');
      } else {
        throw new Error('Invalid registration token');
      }
    } catch (e) {
      if (e instanceof Error) return { error: e.message };
    }
  }

  return { error: 'Unknown error' };
}

export async function resetPassword(_: unknown, formData: FormData): Promise<ActionResult> {
  'use server';
  try {
    const systemModel = await getSystemModel();
    const canSendEmail = await systemModel.canSendEmail();

    const origin = formData.get('_origin');
    let email = formData.get('email');
    if (assertString(email)) email = email.toLowerCase();

    // TODO: Brings back post GTM
    // const { secrets: account } = await getAccountFromUrl({ includeSecrets: true });

    // let domainWhitelist: string[] = [];
    // if (account?.settings) domainWhitelist = account.settings?.domainWhitelist || domainWhitelist;

    if (!canSendEmail) return { error: 'Emailing is not enabled in the application' };

    const confirmToken = generateId(15);

    if (!assertString(email) || !validateEmail(email)) {
      return {
        error: 'Invalid email',
      };
    }

    // TODO: Brings back post GTM
    // if (domainWhitelist.length) {
    //   const emailParts = email.split('@');
    //   const emailDomain = emailParts.pop();

    //   if (!emailDomain || !domainWhitelist.includes(emailDomain)) {
    //     return { error: 'Email domain not authorized' };
    //   }
    // }

    const userModel = await getUserModel();
    const existingUser = await userModel.findOne({ email });

    if (!existingUser?._id) {
      return {
        error: 'Invalid email',
      };
    }

    await userModel.updateOne(
      { _id: existingUser._id },
      {
        $set: {
          confirmToken,
        },
      },
    );

    const { smtp_host, smtp_port, smtp_user, smtp_pass } = await systemModel.getSmtpSettings();
    const port = parseInt(smtp_port || '587');

    const transporter = nodemailer.createTransport({
      host: smtp_host,
      port: port,
      secure: port === 465,
      auth: {
        user: smtp_user,
        pass: smtp_pass,
      },
    });

    const confirmURL = new URL(origin + '/confirm-password');
    confirmURL.searchParams.set('confirm', confirmToken);

    await transporter.sendMail({
      from: '"Trussi.AI" <<EMAIL>>',
      to: email + '',
      subject: 'Trussi.AI Confirm Password',
      text:
        'Please click the following link to confirm your password. If this email is unprompted, you can ignore.\n\n' +
        confirmURL.toString(),
    });

    return {
      message: 'Please check your email for the link to confirm your password',
      error: null,
    };
  } catch (e) {
    if (e instanceof Error) {
      return {
        error: e.message,
      };
    }
    return {
      error: 'An unknown error occurred',
    };
  }
}
