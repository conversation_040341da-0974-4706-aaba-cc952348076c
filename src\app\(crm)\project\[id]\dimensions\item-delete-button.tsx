'use client';

import { ChangeEventHandler } from 'react';
import { useFormStatus } from 'react-dom';
import { Trash2 } from 'lucide-react';

import { ItemDeleteButtonProps } from './types';

export function ItemDeleteButton(props: Readonly<ItemDeleteButtonProps>) {
  const { pending } = useFormStatus();

  const onChange: ChangeEventHandler<HTMLInputElement> = function (event) {
    if (pending) return;
    const form = event.currentTarget.form;
    if (form) form.requestSubmit();

    event.currentTarget.checked = false;
  };

  return (
    <div title={`Delete ${props.name}`}>
      <label className="bg-red-600 flex w-6 h-6 justify-center items-center cursor-pointer rounded-lg mr-3">
        <input className="hidden" type="checkbox" name="_delete" value={props.name} onChange={onChange} />
        <Trash2 className="w-4 h-4" />

        <span className="sr-only">Delete {props.name}</span>
      </label>
    </div>
  );
}
