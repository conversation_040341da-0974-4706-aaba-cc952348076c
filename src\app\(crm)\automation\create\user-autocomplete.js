'use client';

document.addEventListener('DOMContentLoaded', function () {
  // Find all user autocomplete fields
  const autocompleteFields = document.querySelectorAll('.user-autocomplete input');

  // Create a debounce function to limit API calls
  function debounce(func, wait) {
    let timeout;
    return function (...args) {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), wait);
    };
  }

  // Function to fetch user suggestions
  async function fetchUserSuggestions(query, type) {
    try {
      // This would be replaced with your actual API endpoint
      const response = await fetch(`/api/users/suggestions?query=${encodeURIComponent(query)}&type=${type}`);
      if (!response.ok) throw new Error('Failed to fetch suggestions');
      return await response.json();
    } catch (error) {
      logger.error('Error fetching user suggestions:', error);
      return [];
    }
  }

  // Function to create and show suggestions dropdown
  function showSuggestions(input, suggestions, type) {
    // Remove any existing dropdown
    const existingDropdown = document.getElementById('autocomplete-dropdown');
    if (existingDropdown) existingDropdown.remove();

    // Create dropdown container
    const dropdown = document.createElement('div');
    dropdown.id = 'autocomplete-dropdown';
    dropdown.className = 'absolute z-50 bg-white border rounded-md shadow-lg mt-1 w-full max-h-60 overflow-y-auto';

    // Add suggestions to dropdown
    if (suggestions.length === 0) {
      const noResults = document.createElement('div');
      noResults.className = 'px-4 py-2 text-sm text-gray-500';
      noResults.textContent = 'No results found';
      dropdown.appendChild(noResults);
    } else {
      suggestions.forEach((suggestion) => {
        const item = document.createElement('div');
        item.className = 'px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm';

        if (type === 'email') {
          item.textContent = `${suggestion.name} <${suggestion.email}>`;
        } else {
          item.textContent = `${suggestion.name} (${suggestion.phone})`;
        }

        item.addEventListener('click', () => {
          // Get current value and add the new suggestion
          const currentValue = input.value;
          const values = currentValue
            .split(',')
            .map((v) => v.trim())
            .filter((v) => v);

          // Add the new value
          const newValue = type === 'email' ? suggestion.email : suggestion.phone;
          if (!values.includes(newValue)) {
            values.push(newValue);
          }

          // Update input value
          input.value = values.join(', ');

          // Remove dropdown
          dropdown.remove();
        });

        dropdown.appendChild(item);
      });
    }

    // Position and append dropdown
    const rect = input.getBoundingClientRect();
    dropdown.style.width = `${rect.width}px`;

    // Append to body and position
    document.body.appendChild(dropdown);
    dropdown.style.position = 'absolute';
    dropdown.style.left = `${rect.left}px`;
    dropdown.style.top = `${rect.bottom + window.scrollY}px`;

    // Add event listener to close dropdown when clicking outside
    document.addEventListener('click', function closeDropdown(e) {
      if (!dropdown.contains(e.target) && e.target !== input) {
        dropdown.remove();
        document.removeEventListener('click', closeDropdown);
      }
    });
  }

  // Add event listeners to autocomplete fields
  autocompleteFields.forEach((input) => {
    const field = input.closest('.user-autocomplete');
    const type = field.dataset.autocompleteType;

    // Debounced input handler
    const handleInput = debounce(async () => {
      const query = input.value.split(',').pop().trim();
      if (query.length < 2) return;

      const suggestions = await fetchUserSuggestions(query, type);
      showSuggestions(input, suggestions, type);
    }, 300);

    input.addEventListener('input', handleInput);
  });
});
