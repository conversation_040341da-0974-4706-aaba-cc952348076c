import { Metadata, ResolvingMetadata } from 'next';
import Link from 'next/link';

import { QuoteMutateForm } from '@/lib/quote/mutate';

import { getData } from './helpers';
import { CreateProjectQuoteProps } from './types';

export async function generateMetadata(_: unknown, parent: ResolvingMetadata): Promise<Metadata> {
  const parentMetadata = await parent;

  return {
    title: 'Create | ' + parentMetadata.title?.absolute,
  };
}

export default async function CreatePage(props: Readonly<CreateProjectQuoteProps>) {
  const { createAction } = await getData(props.params.id);

  return (
    <QuoteMutateForm
      label="Create New Quote"
      action={createAction}
      excludeLocation
      footer={<Link href={`/project/${props.params.id}/quote-flow`}>Back to Quote Flow</Link>}
    />
  );
}
