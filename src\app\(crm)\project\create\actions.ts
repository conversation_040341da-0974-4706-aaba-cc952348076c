import mongoose from 'mongoose';
import { redirect } from 'next/navigation';

import { db } from '@/lib';
import { assert } from '@/lib/typescript';
import { assertString } from '@/lib/validators';
import { type Account, type AccountDoc, getProjectModel, type User, type UserDoc, PROJECTTYPES } from '@/schemas';
import { PROJECTMILESTONE } from '@/schemas/projects/subdocuments';
import { PROJECTACTIVITYTYPES } from '@/schemas/projects/subdocuments/activity-history/enums';
import { formatAddressForSave } from '@/server/addresses';
import { validateCustomer } from '@/lib/project/helpers';
import { CALENDAR_EVENT_TYPES } from '@/lib/calendar/constants';

import { validatePermissions } from './permissions';

export async function create(_: unknown, formData: FormData): Promise<{ error: string | null }> {
  'use server';
  let canInputCoords = false;
  let account: Account | null = null;
  let user: User | null = null;

  try {
    ({ canInputCoords, account, user } = await validatePermissions());
  } catch (error) {
    if (error instanceof Error) return { error: error.message };

    return { error: 'Unknown error' };
  }

  let _id = null;
  const { firstName, lastName, email, phone, existingCustomer, customerId, location, assignedTo } =
    Object.fromEntries(formData);

  // Use customerId if provided (from URL param), otherwise use existingCustomer from the form
  const customerIdToUse = customerId || existingCustomer;

  // Log the customer ID to help with debugging
  // Debugging: Using customer ID
  // console.log("Using customer ID:", customerIdToUse);

  if (!mongoose.isValidObjectId(location?.toString())) {
    return { error: 'Location is required' };
  }

  if (!assignedTo || !mongoose.isValidObjectId(assignedTo?.toString())) {
    return { error: 'Assignment is required. A lead must be assigned to prevent it from being overlooked.' };
  }

  try {
    assert<AccountDoc>(account, !!account);
    assert<UserDoc>(user, !!user);

    await db();

    const projectModel = await getProjectModel();

    // Get appointment data from form
    const appointmentTitle = formData.get('appointmentTitle')?.toString() || 'Initial Consultation';
    const appointmentStart = formData.get('appointmentStart')?.toString();
    const appointmentDescription = formData.get('appointmentDescription')?.toString() || '';

    // Only create appointment if start date is provided
    let initialAppointment = null;
    let appointmentActivityEntry = null;

    if (appointmentStart) {
      // Calculate end time (1 hour after start time)
      const startDate = new Date(appointmentStart);
      const endDate = new Date(startDate);
      endDate.setHours(startDate.getHours() + 1);

      // Create initial appointment
      initialAppointment = {
        _id: new mongoose.Types.ObjectId(),
        title: appointmentTitle,
        start: startDate,
        end: endDate,
        type: CALENDAR_EVENT_TYPES.APPOINTMENT, // Set event type directly
        description: appointmentDescription,
        assignedTo: new mongoose.mongo.ObjectId(assignedTo.toString()),
        createdBy: user._id,
        updatedBy: user._id,
      };

      // Create appointment activity entry
      appointmentActivityEntry = {
        _id: new mongoose.Types.ObjectId(),
        type: PROJECTACTIVITYTYPES.FILEUPLOAD, // Using an existing valid activity type
        user: user._id,
        value: `Initial appointment "${appointmentTitle}" created`,
        created: new Date(),
      };
    }

    // Create initial activity history entries for project creation and appointment
    const projectActivityEntry = {
      _id: new mongoose.Types.ObjectId(),
      type: PROJECTACTIVITYTYPES.FILEUPLOAD, // Using an existing valid activity type
      user: user._id,
      value: `Project created`,
      created: new Date(),
    };

    // Appointment activity entry is created conditionally above when appointmentStart exists

    const project = new projectModel({
      account: account._id,
      modifiedBy: user._id,
      createdBy: user._id,
      assignedTo: new mongoose.mongo.ObjectId(assignedTo.toString()),
      jobPriority: formData.get('jobPriority'),
      jobCategory: formData.get('jobCategory'),
      jobType: formData.get('jobType') || PROJECTTYPES.INSURANCE,
      jobSubType: formData.get('jobSubType') || undefined,
      leadSource: formData.get('leadSource'),
      companyName: formData.get('companyName'),
      address: formatAddressForSave({ formData, setCoordinates: canInputCoords }),
      location: new mongoose.mongo.ObjectId(location.toString()),
      milestones: [{ name: PROJECTMILESTONE.LEAD }],
      appointments: initialAppointment ? [initialAppointment] : [],
      activityHistory: appointmentActivityEntry
        ? [projectActivityEntry, appointmentActivityEntry]
        : [projectActivityEntry],
    });

    validateCustomer({ firstName, lastName, email, phone, customerId: customerIdToUse });

    // When customerIdToUse is provided and valid, we use that directly
    if (assertString(customerIdToUse) && mongoose.isValidObjectId(customerIdToUse)) {
      project.set('customer', new mongoose.mongo.ObjectId(customerIdToUse.toString()));
      // Log customer ID using a project-compliant logging mechanism (if applicable)
      // Example: logger.info(`Set customer ID: ${customerIdToUse.toString()}`);
    }
    // Otherwise, if we have customer form details, create an inline customer
    else if (assertString(firstName) && assertString(lastName)) {
      project.set('customer', {
        firstName,
        lastName,
        emailAddresses: assertString(email) ? [email] : [],
        phoneNumbers: assertString(phone) ? [phone] : [],
      });
      // Log customer creation using a project-compliant logging mechanism (if applicable)
      // Example: logger.info(`Created inline customer: ${firstName} ${lastName}`);
    }
    // If we reach here without setting a customer, that's an error
    else {
      throw new Error('No valid customer information provided');
    }

    const response = await project.save();

    _id = response._id;
  } catch (e) {
    if (e instanceof Error) return { error: e.message };

    return { error: 'Unknown error' };
  }

  if (!_id) return { error: 'Failed to create record' };

  redirect(`/project/${_id}`);
}
