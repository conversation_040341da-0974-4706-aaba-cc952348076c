import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function () {
  'use server';
  const { user, account } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not create automations.');

  const canCreate = await can(user, 'create_automation');
  if (!canCreate) throw new ServerError('Forbidden: you can not create automations.');

  return { user, account, canCreate };
});
