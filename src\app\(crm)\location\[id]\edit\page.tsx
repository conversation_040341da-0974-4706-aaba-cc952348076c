import Link from 'next/link';

import { Action, AddressFields, Field, Form, FormButton } from '@/lib/form';
import { ActionFooter } from '@/lib/nav';
import { Widget } from '@/lib/widget';

import { getData } from './helpers';
import { LocationEditProps } from './types';

export default async function LocationEdit({ params }: Readonly<LocationEditProps>) {
  const { location, editAction, canInputCoords, canDelete, account } = await getData(params.id);

  return (
    <Form action={editAction} className="space-y-6">
      <Widget label={location.name}>
        <div className="grid grid-cols-1 gap-4">
          <Field name="name" label="Name" value={location.name} type="text" />
          <AddressFields name="address" value={location.address} required editCoord={canInputCoords} />
        </div>
      </Widget>

      <ActionFooter
        left={<Link href={`/account/${account._id.toString()}/custom-fields`}>Back to Location</Link>}
        right={
          <>
            <FormButton>Save</FormButton>
            {!!canDelete && <Action />}
          </>
        }
      />
    </Form>
  );
}
