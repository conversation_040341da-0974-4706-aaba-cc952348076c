import { notFound } from 'next/navigation';
import Link from 'next/link';

import { DisplayData } from '@/lib/view';
import { EditIcon, Widget } from '@/lib/widget';
import { ActionFooter } from '@/lib/nav';
import { CALENDAR_EVENTS_DICTIONARY } from '@/lib/calendar/constants';
import {
  ORDER_DELIVERY_EVENT_DELIVERY_METHOD_DICTIONARY,
  ORDER_DELIVERY_EVENT_PROCUREMENT_METHOD_DICTIONARY,
} from '@/lib/order/constants';
import { DeliveryEvent } from '@/schemas/projects/subdocuments/delivery-event/types';
import { LaborEvent } from '@/schemas/projects/subdocuments/labor-event/types';
import { Appointment } from '@/schemas/projects/subdocuments/appointment/types';

import { getData } from './actions';
import {
  checkIsDelivery,
  checkIsLabor,
  checkIsAppointment,
  getDeliveryDate,
  getLaborDate,
  getAppointmentDate,
} from './helpers';

interface ViewCalendarEventProps {
  params: {
    eventId: string;
    id: string;
  };
}

export default async function ViewCalendarEvent({ params }: Readonly<ViewCalendarEventProps>) {
  const { appointment, deliveryEvent, laborEvent, canEdit, userTz } = await getData({
    id: params.id,
    eventId: params.eventId,
  });

  if (!deliveryEvent && !laborEvent && !appointment) return notFound();

  const isDelivery = checkIsDelivery(deliveryEvent?.type);
  const isLabor = checkIsLabor(laborEvent?.type);
  const isAppointment = checkIsAppointment(appointment?.type);

  const title = deliveryEvent?.title || laborEvent?.title || appointment?.title;

  return (
    <div className="space-y-6">
      <Widget
        label={title}
        action={canEdit && <EditIcon href={`/project/${params.id}/calendar/${params.eventId}/edit`} />}
      >
        {isDelivery && <DeliveryInformation event={deliveryEvent} tzoffset={userTz || 0} />}
        {isLabor && <LaborInformation event={laborEvent} tzoffset={userTz || 0} />}
        {isAppointment && <AppointmentInformation event={appointment} tzoffset={userTz || 0} />}
      </Widget>
      <ActionFooter left={<Link href={`/project/${params.id}`}>Back to Project</Link>} />
    </div>
  );
}

const DeliveryInformation = ({ event, tzoffset }: { event: DeliveryEvent; tzoffset: number }) => {
  const { start, time, deliveryMethod, description, procurementMethod } = event;
  const timeline = getDeliveryDate(start, time, tzoffset);

  return (
    <>
      <DisplayData label="Event Type" value={CALENDAR_EVENTS_DICTIONARY[event.type]} />
      <DisplayData label="Timeline" value={timeline} />
      <DisplayData label="Delivery Method" value={ORDER_DELIVERY_EVENT_DELIVERY_METHOD_DICTIONARY[deliveryMethod]} />
      <DisplayData
        label="Procurement Method"
        value={ORDER_DELIVERY_EVENT_PROCUREMENT_METHOD_DICTIONARY[procurementMethod]}
      />
      <DisplayData label="Location information" value={description} />
    </>
  );
};

const LaborInformation = ({ event, tzoffset }: { event: LaborEvent; tzoffset: number }) => {
  const {
    crew,
    description,
    event: { start, end },
  } = event;

  const timeline = getLaborDate(start, end, tzoffset);

  return (
    <>
      <DisplayData label="Event Type" value={CALENDAR_EVENTS_DICTIONARY[event.type]} />
      <DisplayData label="Crew" value={crew?.name} />
      <DisplayData label="Timeline" value={timeline} />
      <DisplayData label="Crew instructions" value={description} />
    </>
  );
};

const AppointmentInformation = ({ event, tzoffset }: { event: Appointment; tzoffset: number }) => {
  const { description, assignedTo, start, end } = event;

  const timeline = getAppointmentDate(start, end, tzoffset);

  return (
    <>
      <DisplayData label="Event Type" value={CALENDAR_EVENTS_DICTIONARY[event.type]} />
      <DisplayData label="Assigned To" value={assignedTo?.name} />
      <DisplayData label="Timeline" value={timeline} />
      <DisplayData label="Description" value={description} />
    </>
  );
};
