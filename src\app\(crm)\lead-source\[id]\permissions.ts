import { notFound } from 'next/navigation';
import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { LeadSourceDoc } from '@/schemas';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function (leadSource: LeadSourceDoc | null) {
  'use server';
  const { user, account, isSystemAdmin } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not edit lead source.');

  const sameAccount = account._id.equals(leadSource?.account) || isSystemAdmin;

  const canView = await can(user, 'view_leadsource', sameAccount);
  if (!canView) throw new ServerError('Forbidden: you can not edit lead source.');

  const canEdit = await can(user, 'edit_leadsource');

  if (!leadSource?._id) notFound();

  return { canView, canEdit, user, account, leadSource };
});
