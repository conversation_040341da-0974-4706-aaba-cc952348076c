import { cache } from 'react';

import { getLocation } from '@/server/locations';

import { validatePermissions } from './permissions';

export const getData = cache(async function (id: string) {
  'use server';
  const { data } = await getLocation(id);
  const { canEdit, canView, user, account, location } = await validatePermissions(data);

  return { canEdit, canView, user, account, location };
});
