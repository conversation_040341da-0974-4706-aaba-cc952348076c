import Link from 'next/link';

import { Widget, NotFound } from '@/lib/widget';
import { ActionFooter } from '@/lib/nav';
import { COMMENT_TYPE } from '@/schemas/projects/subdocuments';

import { Comment } from './comment';
import { getData, getTopLevelComments } from './helpers';
import { PageProps } from './types';

export default async function ListProjectComments({ params }: Readonly<PageProps>) {
  const projectId = params.id;
  const { project } = await getData(projectId);
  const { comments } = project;

  const internalComments = comments.filter((comment) => comment.type === COMMENT_TYPE.CRM);
  const crewComments = comments.filter((comment) => comment.type === COMMENT_TYPE.CREW_APP);

  // Filter to only get top-level comments (those with no parent)
  const topLevelInternalComments = getTopLevelComments(internalComments);
  const topLevelCrewComments = getTopLevelComments(crewComments);

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-row gap-6">
        <Widget label="Internal Comments">
          {topLevelInternalComments.length > 0 ? (
            topLevelInternalComments.map((comment) => <Comment key={comment.id} {...{ comment }} />)
          ) : (
            <NotFound>No internal comments</NotFound>
          )}
        </Widget>
        <Widget label="Crew Comments">
          {topLevelCrewComments.length > 0 ? (
            topLevelCrewComments.map((comment) => <Comment key={comment.id} {...{ comment }} />)
          ) : (
            <NotFound>No crew comments</NotFound>
          )}
        </Widget>
      </div>
      <ActionFooter
        left={
          <Link href={`/project/${projectId}`} className="mr-auto">
            Back to Project
          </Link>
        }
      />
    </div>
  );
}
