import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function () {
  'use server';
  const { user, account } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not create dead lead reason.');

  const canCreate = await can(user, 'create_deadleadreason');
  if (!canCreate) throw new ServerError('Forbidden: you can not create dead lead reason.');

  return { canCreate, user, account };
});
