'use server';

import mongoose from 'mongoose';
import { redirect } from 'next/navigation';

import { db } from '@/lib';
import { ActionResult } from '@/lib/form';
import { getTemplateFromFormData } from '@/lib/quote/mutate/save';
import { getProjectModel } from '@/schemas';

import { getData } from './helpers';

export async function edit(id: string, quoteId: string, _: unknown, formData: FormData): Promise<ActionResult> {
  'use server';
  const { _action } = Object.fromEntries(formData);

  try {
    await db();
    const { canDelete } = await getData(id, quoteId);

    if (_action === 'delete') {
      if (!canDelete) throw new Error('Forbidden: you do not have permission to delete this quote.');

      const projectModel = await getProjectModel();
      const project = await projectModel.findOne({ _id: new mongoose.mongo.ObjectId(id) });
      if (!project) throw new Error('No project found');

      const quotes = project.quotes.filter((quote) => !quote._id.equals(quoteId));
      project.set('quotes', quotes);

      await project.save({ timestamps: false });
    } else {
      const projectModel = await getProjectModel();

      const project = await projectModel
        .findOne({ _id: new mongoose.mongo.ObjectId(id) })
        .populate('documents.location');
      if (!project) throw new Error('Not Found: project not found.');

      const quote = project.quotes.find((quote) => quote._id.equals(quoteId));
      if (!quote) throw new Error('Not Found: quote was not found.');

      await getTemplateFromFormData(formData, quote);

      await project.save({ timestamps: false });
    }
  } catch (e) {
    if (e instanceof Error) return { error: e.message };

    return { error: 'Unknown error' };
  }

  if (_action === 'delete') {
    redirect(`/project/${id}/quote-flow`);
  } else {
    redirect(`/project/${id}/quote-flow/${quoteId}`);
  }
}
