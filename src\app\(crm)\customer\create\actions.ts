import { redirect } from 'next/navigation';

import { db } from '@/lib';
import { ActionResult } from '@/lib/form';
import { getCustomerModel } from '@/schemas';
import { validateRequest } from '@/server/auth';

import { findExactPhoneMatch, findSimilarCustomers, formatCustomerForDisplay } from './helpers';
import { validatePermissions } from './permissions';

export type CustomerSimilarity = {
  _id: string | null;
  firstName: string;
  lastName: string;
  phoneNumbers: string[];
  emailAddresses: string[];
  name: string;
};

export interface CustomerCreateResult extends ActionResult {
  similarCustomers?: CustomerSimilarity[];
  duplicatePhone?: boolean;
  formData?: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };
}

export async function create(prevState: unknown, formData: FormData): Promise<CustomerCreateResult> {
  'use server';

  try {
    await validatePermissions();
  } catch (error) {
    if (error instanceof Error) return { error: error.message };
    return { error: 'Unknown error' };
  }

  if (!formData) {
    return { error: 'No form data provided' };
  }

  let _id = null;

  // Get form values with proper error handling
  let firstName = '';
  let lastName = '';
  let email = '';
  let phone = '';

  try {
    firstName = formData.get('firstName')?.toString() || '';
    lastName = formData.get('lastName')?.toString() || '';
    email = formData.get('email')?.toString() || '';
    phone = formData.get('phone')?.toString() || '';
  } catch (e) {
    // Log the error using a logging utility or handle it appropriately
    // Example: logger.error('Error parsing form data:', e);
    return { error: 'Invalid form data format' };
  }

  // Validate required fields
  if (!firstName || !lastName || !phone) {
    return {
      error: 'Please provide first name, last name, and phone number.',
      formData: { firstName, lastName, email, phone },
    };
  }

  try {
    await db();
    const { account } = await validateRequest();
    const customerModel = await getCustomerModel();

    // Format phone number to remove non-digits for consistent checking
    const phoneNumber = phone.replace(/[^0-9]/g, '');

    // Check if phone number already exists before creating
    const exactPhoneMatch = await findExactPhoneMatch({
      phone: phoneNumber,
      accountId: account?._id?.toString(),
    });

    // Check for potential duplicates based on name, email, and similar phone
    const potentialDuplicates = await findSimilarCustomers({
      firstName,
      lastName,
      phone: phoneNumber,
      email,
      accountId: account?._id?.toString(),
    });

    // If we have an exact phone match, this is a blocker
    if (exactPhoneMatch) {
      // Return both exact match and any potential duplicates
      const allMatches = [
        formatCustomerForDisplay(exactPhoneMatch),
        ...potentialDuplicates.map(formatCustomerForDisplay),
      ].filter((match) => match._id !== null);

      return {
        // No error message needed, we'll handle this in the UI
        error: null,
        duplicatePhone: true, // Use a separate property rather than an error message
        similarCustomers: allMatches,
        formData: { firstName, lastName, email, phone },
      };
    }

    // If we only found potential duplicates (no exact phone match)
    // Check if the user is explicitly ignoring warnings
    const ignoreWarning = formData.get('ignoreWarning') === 'true';

    if (potentialDuplicates.length > 0 && !ignoreWarning) {
      return {
        // Warn but don't block the creation
        error: null,
        warning: 'We found similar customers in the system. Please review to avoid duplicates.',
        similarCustomers: potentialDuplicates.map(formatCustomerForDisplay).filter((customer) => customer._id !== null),
        formData: { firstName, lastName, email, phone },
      };
    }

    const payload = await customerModel.create({
      account: account?._id,
      firstName,
      lastName,
      phoneNumbers: [phoneNumber],
      emailAddresses: email ? [email] : [],
    });

    _id = payload?._id;
  } catch (e) {
    if (e instanceof Error) return { error: e.message };

    return { error: 'Unknown error' };
  }

  if (!_id) return { error: 'Failed to create record' };

  redirect(`/customer/${_id}`);
}
