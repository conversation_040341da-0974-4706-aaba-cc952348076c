import Link from 'next/link';

import { Action, Field, Form, FormButton } from '@/lib/form';
import { ActionFooter } from '@/lib/nav';
import { Widget } from '@/lib/widget';
import { PROJECT_INVOICE_STATUS } from '@/schemas/projects/subdocuments';

import { getData } from './helpers';
import { EditInvoicepageProps } from './types';

export default async function QuoteFlowInvoiceEdit({ params: { id, invoiceid } }: Readonly<EditInvoicepageProps>) {
  const { invoice, canDelete, editAction } = await getData(id, invoiceid);

  return (
    <Form action={editAction} className="flex flex-col gap-6">
      <Widget label="Invoice" className="space-y-4">
        <Field type="text" name="name" value={invoice.name} label="Name" />
        <Field
          type="select"
          name="status"
          value={invoice.status}
          label="Status"
          options={Object.values(PROJECT_INVOICE_STATUS)}
        />
      </Widget>

      <ActionFooter
        left={<Link href={`/project/${id}/quote-flow/invoice/${invoiceid}`}>Back to Invoice</Link>}
        right={
          <>
            <FormButton>Save</FormButton>
            {canDelete && <Action />}
          </>
        }
      />
    </Form>
  );
}
