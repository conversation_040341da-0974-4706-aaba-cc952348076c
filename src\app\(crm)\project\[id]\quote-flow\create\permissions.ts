import { notFound } from 'next/navigation';
import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { ProjectDoc } from '@/schemas';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function (project: ProjectDoc | null) {
  'use server';
  const { user, account, isSystemAdmin } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not create project quotes.');

  const sameAccount = account._id.equals(project?.account) || isSystemAdmin;

  const canCreate = await can(user, 'create_quote_on_project', sameAccount);
  if (!canCreate) throw new ServerError('Forbidden: you can not create project quotes.');

  if (!project?._id) notFound();

  return { user, account, project, canCreate };
});
