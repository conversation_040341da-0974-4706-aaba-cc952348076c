import Link from 'next/link';
import type { Metadata, ResolvingMetadata } from 'next/types';

import { ActionFooter } from '@/lib/nav';
import { EditIcon, Widget } from '@/lib/widget';

import { getData } from './helpers';
import type { ProjectInvoiceViewProps } from './types';
import PdfViewer from './invoice-viewer';

export async function generateMetadata(
  props: Readonly<ProjectInvoiceViewProps>,
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const parentMetadata = await parent;
  const { invoice } = await getData(props.params.id, props.params.invoiceid);

  return {
    title: `${invoice.name} | Invoice | ` + parentMetadata.title?.absolute,
  };
}

export default async function ProjectInvoiceView(props: Readonly<ProjectInvoiceViewProps>) {
  const { invoice, canEdit, quickBooksInvoicePdfUrl } = await getData(props.params.id, props.params.invoiceid);

  return (
    <div className="space-y-6">
      <Widget
        label="Invoice"
        action={
          canEdit && <EditIcon href={`/project/${props.params.id}/quote-flow/invoice/${props.params.invoiceid}/edit`} />
        }
      >
        {invoice.name}
      </Widget>

      <PdfViewer pdfUrl={quickBooksInvoicePdfUrl} />

      {/* <iframe
        // src={quickBooksInvoicePdfUrl}
        src={`${quickBooksInvoicePdfUrl}#toolbar=0&navpanes=0&scale=page-width&scale=page-height`}
        width="100%"
        height="100%"
        style={{ background: 'transparent' }}
      />
      <a href={quickBooksInvoicePdfUrl} download="invoice.pdf">
        Download PDF
      </a> */}
      <ActionFooter left={<Link href={`/project/${props.params.id}/quote-flow`}>Back to Quote Flow</Link>} />
    </div>
  );
}
