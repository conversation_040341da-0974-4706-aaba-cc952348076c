'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';

import { Form } from '@/lib/form';

import { CustomerCreateResult } from './actions';

export function CustomerForm({
  children,
  action,
  className,
}: {
  children: React.ReactNode;
  action: (previousState: Record<string, unknown>, formData: FormData) => Promise<CustomerCreateResult>;
  className?: string;
}) {
  const router = useRouter();
  const [formError] = useState<string | null>(null);

  // Simplified form handling - just pass through to the server action
  const handleFormAction = async (prevState: unknown, formData: FormData) => {
    const previousState = prevState as Record<string, unknown>;
    try {
      // Check if the form has an ignoreWarning field
      const ignoreWarning = formData.get('ignoreWarning') === 'true';

      const result = await action(previousState, formData);

      // Handle similar customers by updating the URL
      if (result.similarCustomers && result.similarCustomers.length > 0) {
        // If we have a duplicate phone error, always show it
        const hasDuplicatePhone = result.error === 'duplicate_phone';

        // Only show warning matches if we're not ignoring them and it's not a duplicate phone
        if (!ignoreWarning || hasDuplicatePhone) {
          const params = new URLSearchParams();

          // Handle regular errors
          if (result.error) {
            params.set('error', result.error);
          }

          // Handle duplicate phone as a separate flag, not an error
          if ('duplicatePhone' in result && result.duplicatePhone) {
            params.set('duplicatePhone', 'true');
          }

          if (result.warning) {
            params.set('warning', result.warning);
          }

          try {
            // Safely encode the data
            const similarCustomersJson = JSON.stringify(result.similarCustomers);
            params.set('similarCustomers', encodeURIComponent(similarCustomersJson));

            if (result.formData) {
              const formDataJson = JSON.stringify(result.formData);
              params.set('formData', encodeURIComponent(formDataJson));
            }

            // Navigate to the same page with the parameters
            router.push(`/customer/create?${params.toString()}`);
          } catch (e) {
            // Log the error using a custom logging function or handle it appropriately
          }
        }
      }

      return result;
    } catch (error) {
      // Log the error using a custom logging function or handle it appropriately
      return {
        error: error instanceof Error ? error.message : 'An unknown error occurred',
      };
    }
  };

  return (
    <Form className={className} action={handleFormAction}>
      {formError && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-4">{formError}</div>
      )}
      {children}
    </Form>
  );
}
