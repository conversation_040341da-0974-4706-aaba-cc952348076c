'use server';

import mongoose from 'mongoose';
import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

import { db } from '@/lib';
import { ActionResult } from '@/lib/form';
import { getInsuranceCompanyModel } from '@/schemas';

export async function edit(id: string, canDelete: boolean, _: unknown, formData: FormData): Promise<ActionResult> {
  'use server';
  const { _action } = Object.fromEntries(formData);

  if (_action === 'delete') {
    if (!canDelete) return { error: 'Forbidden: you do not have permissions to delete this insurance company.' };

    try {
      await db();
      const insuranceModel = await getInsuranceCompanyModel();
      await insuranceModel.deleteOne({ _id: new mongoose.mongo.ObjectId(id) });
    } catch (e) {
      if (e instanceof Error) return { error: e.message };

      return { error: 'Unknown error' };
    }

    redirect(`/insurance`);
  }

  try {
    await db();
    const insuranceModel = await getInsuranceCompanyModel();

    const { name } = Object.fromEntries(formData);
    const insurance = await insuranceModel.findById(id);
    if (!insurance) return { error: 'Insurance not found' };

    insurance.set('name', name);

    await insurance.save();
  } catch (e) {
    if (e instanceof Error) return { error: e.message };

    return { error: 'Unknown error' };
  }

  revalidatePath(`/insurance/${id}`);
  redirect(`/insurance`);
}
