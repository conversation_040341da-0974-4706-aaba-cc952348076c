import { Metadata, ResolvingMetadata } from 'next';
import Link from 'next/link';

import { CrewsField, Field, Form, FormButton } from '@/lib/form';
import { ActionFooter } from '@/lib/nav';
import { Widget } from '@/lib/widget';
import { CALENDAR_EVENT_TYPES, CALENDAR_EVENTS_DICTIONARY } from '@/lib/calendar/constants';
import {
  ORDER_DELIVERY_EVENT_PROCUREMENT_METHOD_OPTIONS,
  ORDER_DELIVERY_EVENT_DELIVERY_METHOD_OPTIONS,
  ORDER_DELIVERY_EVENT_TIME_OPTIONS,
  ORDER_DELIVERY_EVENT_TIME,
  ORDER_DELIVERY_EVENT_PROCUREMENT_METHOD,
  ORDER_DELIVERY_EVENT_DELIVERY_METHOD,
} from '@/lib/order/constants';
import { toDatetimeLocal } from '@/lib/formatting/date';
import { AssignedT<PERSON>Field } from '@/lib/form';

import { createAppointment, createLabor, createMaterialDelivery } from './actions';

export async function generateMetadata(_: unknown, parent: ResolvingMetadata): Promise<Metadata> {
  const parentMetadata = await parent;

  return {
    title: 'Create | ' + parentMetadata.title?.absolute,
  };
}

interface CreateEventProps {
  params: { id: string };
  searchParams: { orderId: string; type: string; date: string };
}

export default async function CreateEvent({ params, searchParams }: CreateEventProps) {
  let createAction = createAppointment.bind(null, params.id);
  const { type, orderId, date } = searchParams;

  const isDelivery = type === CALENDAR_EVENT_TYPES.DELIVERY;
  const isLabor = type === CALENDAR_EVENT_TYPES.LABOR;
  const isAppointment = type === CALENDAR_EVENT_TYPES.APPOINTMENT;

  if (isDelivery) {
    createAction = createMaterialDelivery.bind(null, {
      orderId,
      projectId: params.id,
    });
  }

  if (isLabor) {
    createAction = createLabor.bind(null, {
      projectId: params.id,
      orderId,
    });
  }

  if (isAppointment) {
    createAction = createAppointment.bind(null, params.id);
  }

  return (
    <Form action={createAction} className="space-y-6">
      <Widget label="New Calendar Event">
        <div>
          <Field name="title" label="Label" required placeholder="Event Summary" />
          <Field name="type" label="Event Type" required value={CALENDAR_EVENTS_DICTIONARY[type]} readOnly />
          {isDelivery && <DeliveryFields date={date} />}
          {isLabor && <LaborFields />}
          {isAppointment && <AppointmentFields date={date} />}
        </div>
      </Widget>
      <ActionFooter
        left={<Link href={`/project/${params.id}`}>Back to Project</Link>}
        right={<FormButton>Save</FormButton>}
      />
    </Form>
  );
}

const DeliveryFields = ({ date }: { date: string }) => (
  <>
    <Field name="start" label="Start" required type="date" value={date ? toDatetimeLocal(new Date(date)) : undefined} />
    <Field
      name="time"
      label="Delivery Time"
      required
      type="select"
      options={ORDER_DELIVERY_EVENT_TIME_OPTIONS}
      value={ORDER_DELIVERY_EVENT_TIME.ANY}
    />
    <Field
      name="procurementMethod"
      label="Procurement Method"
      required
      type="select"
      options={ORDER_DELIVERY_EVENT_PROCUREMENT_METHOD_OPTIONS}
      value={ORDER_DELIVERY_EVENT_PROCUREMENT_METHOD.DELIVERY}
    />
    <Field
      name="deliveryMethod"
      label="Delivery Method"
      required
      type="select"
      options={ORDER_DELIVERY_EVENT_DELIVERY_METHOD_OPTIONS}
      value={ORDER_DELIVERY_EVENT_DELIVERY_METHOD.ROOF_DROP}
    />
    <Field name="description" label="Location information" />
  </>
);

const LaborFields = () => (
  <>
    <Field name="start" label="Start" required type="date" />
    <Field name="end" label="End" required type="date" />
    <CrewsField />
    <Field name="description" label="Crew instructions" />
  </>
);

const AppointmentFields = ({ date }: { date: string }) => (
  <>
    <Field
      name="start"
      label="Start"
      required
      step={0}
      type="datetime-local"
      value={date ? toDatetimeLocal(new Date(date)) : undefined}
    />
    <Field name="end" label="End" required type="datetime-local" step={0} />
    <AssignedToField />
    <Field name="description" label="Description" />
  </>
);
