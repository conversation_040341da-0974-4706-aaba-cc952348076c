import { Metadata, ResolvingMetadata } from 'next';

import { WidgetEdit } from '@/lib/account';
import { Field, Form } from '@/lib/form';

import { getData } from './helpers';

export async function generateMetadata(_: unknown, parent: ResolvingMetadata): Promise<Metadata> {
  const parentMetadata = await parent;

  return {
    title: 'Authentication | ' + parentMetadata.title?.absolute,
  };
}

export default async function EditPage({ params }: Readonly<{ params: { id: string } }>) {
  const { account, editAction } = await getData(params.id);

  return (
    <Form action={editAction}>
      <WidgetEdit label={account.name} id={account.id}>
        <div className="grid grid-cols-1 gap-4">
          <Field name="domainwhitelist" label="Domain Whitelist" value={account.settings?.domainWhitelist?.join(',')} />

          <div className="flex gap-6">
            <div className="grow">
              <Field name="authBackground" label="Login Background" type="file" accept=".jpg, .jpeg, .png, .webm" />
            </div>

            {account?.settings?.authBackground && (
              <div>
                <img
                  className="w-24 h-24 object-cover"
                  alt={account.documents?.settings?.authBackground?.filename || ''}
                  src={`/file/${account.settings.authBackground}`}
                />
              </div>
            )}
          </div>
        </div>
      </WidgetEdit>
    </Form>
  );
}
