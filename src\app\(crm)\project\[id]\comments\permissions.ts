import { notFound } from 'next/navigation';
import { cache } from 'react';

import { can } from '@/lib/capabilities';
import type { ProjectDoc } from '@/schemas';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function (project: ProjectDoc | null) {
  'use server';

  const { user, account, isSystemAdmin } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not edit projects.');

  if (!project?._id) notFound();

  const sameAccount = account._id.equals(project?.account) || isSystemAdmin;

  const [canPostComment, canDeleteComment, canViewUsers] = await Promise.all([
    can(user, 'post_project_comment', sameAccount, !project?.readOnly),
    can(user, 'delete_project_comment', sameAccount, !project?.readOnly),
    can(user, 'view_user', sameAccount),
  ]);

  return { user, account, project, canPostComment, canDeleteComment, canViewUsers };
});
