import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function validateAutomationListPermissions() {
  'use server';
  const { user, account } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not list automations.');

  const canList = await can(user, 'list_automation');
  if (!canList) throw new ServerError('Forbidden: you can not list automations.');

  const [canEdit, canCreate, canDelete] = await Promise.all([
    can(user, 'edit_automation'),
    can(user, 'create_automation'),
    can(user, 'delete_automation'),
  ]);

  return { canList, canEdit, canCreate, canDelete, user, account };
});
