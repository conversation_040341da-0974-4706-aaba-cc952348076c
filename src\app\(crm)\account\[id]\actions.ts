import mongoose from 'mongoose';
import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

import { ActionResult } from '@/lib/form';
import { assertFile } from '@/lib/validators';
import { getAccountModel, getFileModel } from '@/schemas';

import { validatePermissions } from './permissions';

export async function edit(id: string, _: unknown, formData: FormData): Promise<ActionResult> {
  'use server';
  let canDelete = false;

  try {
    ({ canDelete } = await validatePermissions(id));
  } catch (error) {
    if (error instanceof Error) return { error: error.message };

    return { error: 'Unknown error' };
  }

  const { _action } = Object.fromEntries(formData);

  if (_action === 'delete') {
    if (!canDelete) return { error: 'Forbidden: You can not delete this account.' };

    try {
      const accountModel = await getAccountModel();

      await accountModel.deleteOne({ _id: new mongoose.mongo.ObjectId(id) });
    } catch (e) {
      if (e instanceof Error) return { error: e.message };

      return { error: 'Unknown error' };
    }

    revalidatePath(`/account`);
    redirect(`/account`);
  }

  try {
    const accountModel = await getAccountModel();
    const fileModel = await getFileModel();

    const account = await accountModel.findOne({ _id: new mongoose.mongo.ObjectId(id) });
    if (!account) throw new Error(`Account ${id} does not exist`);

    account.prefix = formData.get('prefix') + '';
    account.name = formData.get('name') + '';

    const logo = formData.get('logo');
    if (logo && assertFile(logo) && logo.size > 0) {
      if (logo.size > 5242880) throw new Error('Logo cannot exceed 5MB');
      const buffer = Buffer.from(await logo.arrayBuffer());
      const upload = await fileModel.create({
        account: account._id,
        bin: buffer,
        filename: logo.name,
        mimetype: logo.type,
        protected: false,
      });

      if (upload._id) account.set('settings.logo', upload._id);
    }

    await account.save();
  } catch (e) {
    if (e instanceof Error) {
      return { error: e.message };
    }

    return { error: 'Unknown error' };
  }

  revalidatePath(`/account/${id}`);
  return { error: null };
}
