import Link from 'next/link';

import { serializeCalendarEvents } from '@/lib/calendar/serialize';
import { ActionFooter } from '@/lib/nav';

import { getData } from './actions';
import { CalendarAgenda } from './calendar-agenda';

interface CalendarProps {
  params: {
    id: string;
  };
}

export default async function Calendar({ params }: CalendarProps) {
  const { data: project, user } = await getData(params.id);
  const userTz = user?.tzoffset || 0;

  const events = serializeCalendarEvents(project, userTz);

  return (
    <div className="space-y-6">
      <CalendarAgenda events={events} projectId={params.id} />
      <ActionFooter left={<Link href={`/project/${params.id}`}>Back to Project</Link>} />
    </div>
  );
}
