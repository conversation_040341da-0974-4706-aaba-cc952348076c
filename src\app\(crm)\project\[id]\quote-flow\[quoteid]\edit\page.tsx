import Link from 'next/link';

import { QuoteMutateForm } from '@/lib/quote/mutate';

import { getData } from './helpers';
import { EditQuotepageProps } from './types';

export default async function EditQuotePage({ params: { id, quoteid } }: Readonly<EditQuotepageProps>) {
  const { quote, editAction, canDelete } = await getData(id, quoteid);
  const label = `${quote.name} Section`;

  return (
    <QuoteMutateForm
      label={label}
      canDelete={canDelete}
      quote={quote}
      action={editAction}
      excludeLocation
      footer={<Link href={`/project/${id}/quote-flow/${quoteid}`}>Back to Quote</Link>}
    />
  );
}
