#!/bin/bash

mongo1=mongo1:27017
mongo2=mongo2:27018
mongo3=mongo3:27019

until mongosh --host ${mongo1} --eval "db.runCommand({ping: 1})"
do
    echo "waiting for a mongo instance to boot"
    sleep 1
done

echo "now going to initiate a replica set"

mongosh --host ${mongo1} <<EOF
rs.initiate({
    "_id": "rs0",
    "version": 1,
    "members": [
        {
            "_id": 0,
            "host": "${mongo1}",
            "priority": 3
        },
        {
            "_id": 1,
            "host": "${mongo2}",
            "priority": 2
        },
        {
            "_id": 2,
            "host": "${mongo3}",
            "priority": 1
        }
    ]
});
EOF

while ! [[ $(mongosh --host "${mongo1}" --eval "db.isMaster().ismaster") == "true" ]]
do 
    echo "waiting for elections to finish"
    sleep 1
done

echo "creating a root user"

mongosh --host ${mongo1} <<EOF
use admin;
db.createUser({user: "local", pwd: "local", roles: [{role: "root", db: "admin"}]});
EOF
