import { generateId } from 'lucia';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import nodemailer from 'nodemailer';

import { db } from '@/lib';
import type { ActionResult } from '@/lib/auth';
import { getLowestRole, ROLES } from '@/lib/capabilities';
import { assertString } from '@/lib/validators';
import { AccountDocWithSecrets, getSystemModel } from '@/schemas';
import { getAccountModel } from '@/schemas/accounts';
import { TOKEN_TYPE } from '@/schemas/sessions/enums';
import { getUserModel } from '@/schemas/users';
import { hashPassword, luciaSession } from '@/server';
import { getAccountFromUrl } from '@/server/accounts';
import { INVITE_STATUS } from '@/schemas/users/enums';

export async function verifyUser(_: unknown, formData: FormData): Promise<ActionResult> {
  'use server';

  let redirectTo = '/';
  const confirm = formData.get('_confirm');
  const password = formData.get('password');
  const confirmPassword = formData.get('confirm-password');

  try {
    const userModel = await getUserModel();
    const accountModel = await getAccountModel();

    // Confirm the user.
    const user = await userModel.findOne({ confirmToken: confirm + '' });
    if (!user?._id) throw new Error('Invalid registration token');

    const hashedPassword = await hashPassword(password, confirmPassword);

    user.password = hashedPassword;
    user.verified = true;
    user.confirmToken = '';
    user.inviteStatus = INVITE_STATUS.ACCEPTED;

    await user.save();

    const session = await luciaSession.createSession(user._id, { type: TOKEN_TYPE.COOKIE });
    const sessionCookie = luciaSession.createSessionCookie(session.id);
    cookies().set(sessionCookie.name, sessionCookie.value, sessionCookie.attributes);

    const accountCount = await accountModel.countDocuments();
    if (!accountCount) redirectTo = '/account/create';
  } catch (e) {
    if (e instanceof Error) return { error: e.message };

    return { error: 'Unknown error' };
  }

  redirect(redirectTo);
}

export async function register(_: unknown, formData: FormData): Promise<ActionResult> {
  'use server';
  let account: AccountDocWithSecrets | null = null;
  let redirectTo = '/';

  try {
    ({ secrets: account } = await getAccountFromUrl({ includeSecrets: true }));
  } catch {}

  try {
    await db();
    let verificationMessage = '';

    const [systemModel, userModel] = await Promise.all([getSystemModel(), getUserModel()]);
    const userCount = await userModel.countDocuments();
    const canSendEmail = await systemModel.canSendEmail();

    const origin = formData.get('_origin');
    const form = Object.fromEntries(formData.entries());

    const email = assertString(form.email) ? form.email : '';
    const password = assertString(form.password) ? form.password : '';
    const confirmPassword = assertString(form['config-password']) ? form['confirm-password'] : '';

    let verified = true;
    let confirmToken = '';
    let hashedPassword = '';

    if (canSendEmail) {
      verified = false;
      confirmToken = generateId(15);
    }

    let defaultRole = await getLowestRole();
    if (!account?._id) defaultRole = ROLES.ADMIN;
    if (verified && confirmPassword && password) hashedPassword = await hashPassword(password, confirmPassword);

    const [firstName, lastName] = userCount > 0 ? [undefined, undefined] : ['System', 'Admin'];

    const result = await userModel.create({
      firstName,
      lastName,
      email,
      password: hashedPassword,
      verified,
      confirmToken,
      account: account?._id,
      role: defaultRole,
      inviteStatus: INVITE_STATUS.ACCEPTED,
    });

    if (!result?._id) throw new Error('User not created.');

    if (!canSendEmail) {
      const session = await luciaSession.createSession(result._id, { type: TOKEN_TYPE.COOKIE });
      const sessionCookie = luciaSession.createSessionCookie(session.id);
      cookies().set(sessionCookie.name, sessionCookie.value, sessionCookie.attributes);

      if (!account?._id) redirectTo = '/account/create';
    } else if (account) {
      const { smtp_host, smtp_port, smtp_user, smtp_pass } = await systemModel.getSmtpSettings();
      const port = parseInt(smtp_port || '587');

      const transporter = nodemailer.createTransport({
        host: smtp_host,
        port: port,
        secure: port === 465,
        auth: {
          user: smtp_user,
          pass: smtp_pass,
        },
      });

      const registrationUrl = new URL(origin + '/register');
      registrationUrl.searchParams.set('confirm', confirmToken);

      await transporter.sendMail({
        from: '"Trussi.AI" <<EMAIL>>',
        to: email + '',
        subject: 'Trussi.AI Registration',
        text: 'Please click the following link to register:\n\n' + registrationUrl.toString(),
      });

      verificationMessage = 'Please check your email for a registration link.';
    }

    if (verificationMessage)
      return {
        message: verificationMessage,
        error: null,
      };
  } catch (e) {
    if (e instanceof Error) return { error: e.message };
    return { error: 'Unknown error' };
  }

  redirect(redirectTo);
}
