# Docker

## Developing in containers

To start developing in the containers, run:

```sh
docker compose -f docker/dev.yml up -d && docker compose -f docker/dev.yml logs dev -f
```

When no longer needed, containers can be purged together with the data:

```sh
docker compose -f docker/dev.yml down -v
```

## Testing in containers

To run the zero-config end-to-end test suite:

```sh
docker compose -f docker/test.yml up -d && docker compose -f docker/test.yml logs test -f
```
