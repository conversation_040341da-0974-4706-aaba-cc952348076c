import Link from 'next/link';
import { Metadata, ResolvingMetadata } from 'next/types';

import { Field, Form, FormButton } from '@/lib/form';
import { ActionFooter } from '@/lib/nav';
import { Widget } from '@/lib/widget';
import { UNIT } from '@/schemas/quote-template';

import { getData } from './helpers';
import { ItemDeleteButton } from './item-delete-button';
import { DimensionFieldProps, PageProps } from './types';

export async function generateMetadata(_: unknown, parent: ResolvingMetadata): Promise<Metadata> {
  const parentMetadata = await parent;

  return {
    title: 'Dimensions | ' + parentMetadata.title?.absolute,
  };
}

export default async function ProjectDimensions({ params }: Readonly<PageProps>) {
  const { project, editAction } = await getData(params.id);

  const { dimensions } = project;

  return (
    <Form action={editAction} className="flex flex-col gap-6">
      <Widget label="Dimensions">
        <div className="flex flex-col gap-4">
          {dimensions.map((dimension) => (
            <DimensionField key={dimension.name} name={dimension.name} value={dimension.value} unit={dimension.unit} />
          ))}

          <DimensionField />
        </div>
      </Widget>

      <ActionFooter
        left={<Link href={`/project/${params.id}`}>Back to Project</Link>}
        right={<FormButton>Save</FormButton>}
      />
    </Form>
  );
}

function DimensionField(props: Readonly<DimensionFieldProps>) {
  return (
    <div className="grid md:grid-cols-12 gap-4 items-center">
      <div className="md:col-span-7">
        <Field label="Name" name="name[]" value={props.name} type="text" />
      </div>
      <div className="md:col-span-2">
        <Field label="Value" name="value[]" value={props.value} type="number" />
      </div>
      <div className="md:col-span-2">
        <Field label="Unit" name="unit[]" value={props.unit} type="select" options={Object.values(UNIT)} />
      </div>
      {props.name ? (
        <div className="flex justify-end self-end mb-1.5">
          <ItemDeleteButton name={props.name} />
        </div>
      ) : (
        <div />
      )}
    </div>
  );
}
