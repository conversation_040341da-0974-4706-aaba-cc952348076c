import { notFound } from 'next/navigation';
import { cache } from 'react';

import { SIGNATURE_TYPE } from '@/schemas/projects/subdocuments/signature';
import { getProject } from '@/server/projects';

import { validatePermissions } from './permissions';

export const getData = cache(async function (projectId: string, fileId: string) {
  'use server';
  const { data } = await getProject(projectId);
  const props = await validatePermissions(data);

  const file = props.project.files.find((f) => f.file.toString() === fileId);
  if (!file || !file.documents?.file) notFound();

  const fileDoc = file.documents.file;

  return { ...props, file, fileDoc };
});

export const getSignatureName = cache(async function _getSignatureName(
  type: SIGNATURE_TYPE,
  projectId: string,
  fileId: string,
) {
  'use server';
  const { project, file, user, canSignAsAdmin } = await getData(projectId, fileId);
  if (!project) return '';

  let name = 'N/A';
  if (type === SIGNATURE_TYPE.CUSTOMER) name = project.documents?.customer?.name || 'N/A';
  if (type === SIGNATURE_TYPE.ADMIN) {
    name = 'Account Admin';
    const existingSignature = file.signatures.find((signature) => signature.type === type);
    if (existingSignature && existingSignature.documents?.createdBy?.name) {
      name = existingSignature.documents.createdBy.name;
    }

    if (canSignAsAdmin) name = user.name;
  }

  if (type === SIGNATURE_TYPE.PM) name = project.documents?.assignedTo?.name || 'N/A';

  return name;
});
