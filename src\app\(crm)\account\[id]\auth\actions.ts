import mongoose from 'mongoose';
import { revalidatePath } from 'next/cache';

import { db } from '@/lib';
import { assertFile } from '@/lib/validators';
import { getAccountModel, getFileModel } from '@/schemas';

import { validatePermissions } from './permissions';

export async function edit(id: string, _: unknown, formData: FormData): Promise<{ error: string | null }> {
  'use server';

  try {
    await db();
    await validatePermissions(id);

    const accountModel = await getAccountModel();
    const fileModel = await getFileModel();

    const account = await accountModel.findOne({ _id: new mongoose.mongo.ObjectId(id) });
    if (!account) throw new Error(`Account ${id} does not exist`);

    const domainWhitelist = formData.get('domainwhitelist');

    if (domainWhitelist) {
      const domainWhitelistValues = (domainWhitelist + '')
        .split(',')
        .map((x) => x.trim())
        .filter((x) => !!x);

      account.set('settings.domainWhitelist', domainWhitelistValues);
    }

    const authBackground = formData.get('authBackground');
    if (authBackground && assertFile(authBackground) && authBackground.size > 0) {
      if (authBackground.size > 5242880) throw new Error('Login Background exceeds 5MB');
      const buffer = Buffer.from(await authBackground.arrayBuffer());
      const upload = await fileModel.create({
        account: account._id,
        bin: buffer,
        filename: authBackground.name,
        mimetype: authBackground.type,
        protected: false,
      });

      if (upload._id) account.set('settings.authBackground', upload._id);
    }

    await account.save();
  } catch (e) {
    if (e instanceof Error) return { error: e.message };

    return { error: 'Unknown error' };
  }

  revalidatePath(`/account/${id}/auth`);
  return { error: null };
}
