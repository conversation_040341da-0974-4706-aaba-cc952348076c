import Link from 'next/link';

import { <PERSON>, Wrapper } from '@/lib/auth';
import { FormButton } from '@/lib/form';

import { login } from './actions';
import { getData } from './helpers';

export default async function Login() {
  const { canSendEmail } = await getData();
  return (
    <Wrapper
      title="Welcome Back"
      description="Enter your email and password below to login to your account"
      action={login}
    >
      <Field name="email" label="Email" placeholder="Enter your email" type="email" required autoComplete="email" />

      <Field
        required
        label="Password"
        placeholder="Enter your password"
        name="password"
        type="password"
        autoComplete="current-password"
      />

      <FormButton className="w-full">Login</FormButton>

      <p className="flex justify-center items-center gap-6">
        {canSendEmail && (
          <Link className="text-primary hover:underline cursor-pointer" href="/reset-password">
            reset password
          </Link>
        )}
        {/* TODO: Brings back post GTM */}
        {/* <Link role="link" className="text-orange-500 hover:underline cursor-pointer" href="/register">
          register
        </Link> */}
      </p>
    </Wrapper>
  );
}
