'use server';

import mongoose from 'mongoose';
import { redirect } from 'next/navigation';

import { ActionResult } from '@/lib/form';
import { validateEnum } from '@/lib/typescript';
import { assertString } from '@/lib/validators';
import { getProjectModel } from '@/schemas';
import { PROJECT_INVOICE_STATUS } from '@/schemas/projects/subdocuments';

import { getData } from './helpers';

export async function edit(id: string, invoiceId: string, _: unknown, formData: FormData): Promise<ActionResult> {
  'use server';
  const { _action } = Object.fromEntries(formData);

  try {
    const { canDelete } = await getData(id, invoiceId);

    if (_action === 'delete') {
      if (!canDelete) throw new Error('Forbidden: you do not have permission to delete this invoice.');

      const projectModel = await getProjectModel();
      const project = await projectModel.findOne({ _id: new mongoose.mongo.ObjectId(id) });
      if (!project) throw new Error('No project found');

      const invoices = project.invoices.filter((invoice) => !invoice._id.equals(invoiceId));
      project.set('invoices', invoices);

      await project.save({ timestamps: false });
    } else {
      const projectModel = await getProjectModel();

      const project = await projectModel
        .findOne({ _id: new mongoose.mongo.ObjectId(id) })
        .populate('documents.location');
      if (!project) throw new Error('Not Found: project not found.');

      const invoice = project.invoices.find((invoice) => invoice._id.equals(invoiceId));
      if (!invoice) throw new Error('Not Found: invoice was not found.');

      const form = Object.fromEntries(formData.entries());
      const name = form.name && assertString(form.name) ? form.name : '';
      const status = validateEnum(form.status, PROJECT_INVOICE_STATUS) ? form.status : PROJECT_INVOICE_STATUS.UNPAID;

      invoice.name = name;
      invoice.status = status;

      await project.save({ timestamps: false });
    }
  } catch (e) {
    if (e instanceof Error) return { error: e.message };

    return { error: 'Unknown error' };
  }

  if (_action === 'delete') {
    redirect(`/project/${id}/quote-flow`);
  } else {
    redirect(`/project/${id}/quote-flow/invoice/${invoiceId}`);
  }
}
