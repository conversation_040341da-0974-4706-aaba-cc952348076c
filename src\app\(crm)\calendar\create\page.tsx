import { Metadata, ResolvingMetadata } from 'next';
import Link from 'next/link';

import { Field, Form, FormButton } from '@/lib/form';
import { ActionFooter } from '@/lib/nav';
import { Widget } from '@/lib/widget';
import { CALENDAR_EVENT_TYPES, CALENDAR_EVENTS_DICTIONARY } from '@/lib/calendar/constants';
import { toDatetimeLocal } from '@/lib/formatting/date';

import { create } from './actions';

export async function generateMetadata(_: unknown, parent: ResolvingMetadata): Promise<Metadata> {
  const parentMetadata = await parent;

  return {
    title: 'Create | ' + parentMetadata.title?.absolute,
  };
}

interface CreatePageProps {
  searchParams: { date: string };
}

export default async function CreatePage({ searchParams }: CreatePageProps) {
  return (
    <Form action={create} className="space-y-6">
      <Widget label="New Calendar Event">
        <Field name="summary" label="Summary" required placeholder="Event Summary" />
        <Field
          name="start"
          label="Start"
          required
          type="datetime-local"
          step={0}
          value={searchParams.date ? toDatetimeLocal(new Date(searchParams.date)) : undefined}
        />
        <Field name="end" label="End" required type="datetime-local" step={0} />
        <Field name="description" label="Description" />
        <Field
          name="type"
          label="Event Type"
          required
          value={CALENDAR_EVENTS_DICTIONARY[CALENDAR_EVENT_TYPES.APPOINTMENT]}
          readOnly
        />
      </Widget>
      <ActionFooter left={<Link href="/calendar">Back to Calendar</Link>} right={<FormButton>Save</FormButton>} />
    </Form>
  );
}
