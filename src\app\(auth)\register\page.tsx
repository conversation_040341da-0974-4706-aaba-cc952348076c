import Link from 'next/link';

import { Field, Wrapper } from '@/lib/auth';
import { FormButton, Passwords } from '@/lib/form';

import { register, verifyUser } from './actions';
import { getEmailSettings } from './helpers';
import { Origin } from './origin';

export default async function Register({ searchParams }: Readonly<{ searchParams: { confirm?: string } }>) {
  const { canSendEmail } = await getEmailSettings();

  return (
    <Wrapper title="Register" action={searchParams?.confirm ? verifyUser : register}>
      {searchParams?.confirm ? (
        <>
          <Passwords required />
          <input type="hidden" name="_confirm" value={searchParams.confirm} />
        </>
      ) : (
        <Field name="email" label="Email" placeholder="Enter your email" type="email" required autoComplete="email" />
      )}

      {!canSendEmail && <Passwords required />}

      <Origin />

      <FormButton>Register</FormButton>

      <p className="text-center">
        <Link className="text-orange-500 hover:underline cursor-pointer" href="/login">
          login
        </Link>
      </p>
    </Wrapper>
  );
}
