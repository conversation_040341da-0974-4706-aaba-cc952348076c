import Link from 'next/link';
import { RotateCcw, PlusIcon } from 'lucide-react';
import Image from 'next/image';

import { Widget } from '@/lib/widget';
import { DisplayData } from '@/lib/view';
import { Map } from '@/lib/map';
import { ActionFooter } from '@/lib/nav';
import { ActionResult, Button, Field, Form, FormButton } from '@/lib/form';

import { getData, serializeMeasurements } from './helpers';
import { EagleviewReportProps } from './types';

export default async function EagleviewReport({ params }: EagleviewReportProps) {
  const { editAction, copyToDimensionsAction, projectDimensions, report, image } = await getData(params.id);
  const measurements = serializeMeasurements(report?.TotalMeasurements, projectDimensions);

  const {
    TotalMeasurements,
    Street,
    City,
    State,
    Zip,
    Latitude,
    Longitude,
    DatePlaced,
    Status,
    MeasurementRequestType,
    Area,
    ReportId,
    ReportDownloadLink,
  } = report;

  return (
    <div className="space-y-6">
      <Widget label="Report Location">
        <div className="grid grid-cols-3 gap-6">
          <div className="col-span-1">
            <DisplayData label="Street" value={Street} />
            <DisplayData label="City" value={City} />
            <DisplayData label="State" value={State} />
            <DisplayData label="Zip" value={Zip} />
            <DisplayData label="Latitude" value={Latitude} />
            <DisplayData label="Longitude" value={Longitude} />
          </div>

          <Map
            className="w-full h-full min-h-64 rounded-lg overflow-hidden col-span-2"
            lat={Latitude}
            lng={Longitude}
          />
        </div>
      </Widget>
      <Widget label="Report Info">
        <DisplayData label="Report Date" value={DatePlaced} />
        <DisplayData label="Report Status" value={Status} />
        <DisplayData label="Report Type" value={MeasurementRequestType} />
        <DisplayData label="Report Area" value={Area} />
      </Widget>
      <Widget label="Report Details" className="flex flex-col gap-2">
        <DisplayData label="Report ID" value={ReportId} />
        <div className="w-1/3 flex flex-col gap-4">
          <Image src={image} alt="Eagleview Report" className="w-full" width={300} height={300} />
          <Link href={ReportDownloadLink} target="_blank">
            <Button>Download from Eagleview</Button>
          </Link>
        </div>
      </Widget>
      <Widget label="Report Measurements">
        {TotalMeasurements.BuildingName && <DisplayData label="Building Name" value={TotalMeasurements.BuildingName} />}
        {TotalMeasurements.Area && <DisplayData label="Area" value={TotalMeasurements.Area} />}
        {TotalMeasurements.PrimaryPitch && <DisplayData label="Primary Pitch" value={TotalMeasurements.PrimaryPitch} />}
        {TotalMeasurements.WallMeasurement && (
          <DisplayData label="Wall Measurement" value={TotalMeasurements.WallMeasurement} />
        )}
        {TotalMeasurements.AreaValue && <DisplayData label="Area Value" value={TotalMeasurements.AreaValue} />}
        {TotalMeasurements.PitchValue && <DisplayData label="Pitch Value" value={TotalMeasurements.PitchValue} />}

        {measurements.map(({ name, value, unit, update }) => (
          <DimensionField key={name} name={name} value={value} unit={unit} update={update} action={editAction} />
        ))}
        <div className="flex justify-end">
          <Form action={copyToDimensionsAction} className="mt-4">
            <FormButton>Copy all to Dimensions</FormButton>
          </Form>
        </div>
      </Widget>
      <ActionFooter left={<Link href={`/project/${params.id}`}>Back to Project</Link>} />
    </div>
  );
}

export interface DimensionFieldProps {
  name: string;
  value: number;
  unit: string;
  update: boolean;
  action: (prevState: unknown, formData: FormData) => Promise<ActionResult>;
}

function DimensionField({ name, value, unit, update, action }: Readonly<DimensionFieldProps>) {
  return (
    <Form action={action}>
      <div className="grid md:grid-cols-12 gap-4 items-center">
        <div className="md:col-span-7">
          <Field label="Name" name="name" value={name} type="text" readOnly />
        </div>
        <div className="md:col-span-2">
          <Field label="Value" name="value" value={value} type="text" readOnly />
        </div>
        <div className="md:col-span-2">
          <Field label="Unit" name="unit" value={unit} type="text" readOnly />
        </div>
        <div className="flex justify-end self-end">
          <FormButton>{update ? <RotateCcw size={16} /> : <PlusIcon size={16} />}</FormButton>
        </div>
      </div>
    </Form>
  );
}
