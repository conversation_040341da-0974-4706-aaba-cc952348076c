import { notFound } from 'next/navigation';
import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { ProjectDoc } from '@/schemas';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function (project: ProjectDoc | null) {
  'use server';

  const { user, account, isSystemAdmin } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not view project signatures.');

  const sameAccount = account._id.equals(project?.account) || isSystemAdmin;

  const canView = await can(user, 'view_Project', sameAccount);
  if (!canView) throw new ServerError('Forbidden: you can not view project signatures.');

  const [canSignWithCustomerInperson, canSignAsAdmin, canSignAsPm] = await Promise.all([
    can(user, 'sign_with_customer_inperson', sameAccount),
    can(user, 'sign_as_admin', sameAccount),
    can(user, 'sign_as_pm', sameAccount),
  ]);

  if (!project?._id) notFound();

  return {
    canView,
    user,
    account,
    project,
    canSignWithCustomerInperson,
    canSignAsAdmin,
    canSignAsPm,
  };
});
