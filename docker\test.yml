include:
  - ./replica-set.yml

services:
  test:
    depends_on:
      - init
    image: mcr.microsoft.com/playwright:v1.44.1-jammy
    restart: no
    working_dir: /app
    ports:
      - 3000:3000
    volumes:
      - ../:/app
      - /dev/null:/app/.env
    environment:
      TZ: utc
      NODE_ENV: production
      MONGODB_URI: ${MONGODB_URI:-**************************************************************************************************************}
    entrypoint: ['/bin/sh', '-c', 'npm ci --include=dev && npm run build && npm run test:e2e']
