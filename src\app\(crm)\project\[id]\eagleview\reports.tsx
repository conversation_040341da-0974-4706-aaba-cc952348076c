import Link from 'next/link';
import Image from 'next/image';

import { Widget } from '@/lib/widget';
import { Form, FormButton, Warning } from '@/lib/form';

import { requestReport, updateReportStatus, getReport } from './actions';

interface ReportsProps {
  projectId: string;
}

export async function Reports({ projectId }: Readonly<ReportsProps>) {
  const { report, image } = await getReport(projectId);
  const hasReport = !!report;
  const hasError = hasReport && report.status === 'Error';
  const isCompleted = hasReport && report.status === 'Completed';
  const label = 'Eagleview Report';

  if (hasError) {
    return (
      <Widget label={label}>
        <Warning>This report does not have critical information. Please contact Eagleview</Warning>
      </Widget>
    );
  }

  if (!hasReport) {
    return (
      <Widget label={label}>
        <Form action={requestReport.bind(null, projectId)}>
          <FormButton>Order Report</FormButton>
        </Form>
      </Widget>
    );
  }

  return (
    <Widget label={label}>
      <div className="flex flex-col gap-2">
        <Form action={updateReportStatus.bind(null, projectId)}>
          <FormButton>Update Status</FormButton>
        </Form>
        <div className="text-center p-4">Status: {report.status}</div>
        {isCompleted && (
          <>
            {image && <Image src={image} alt="Eagleview Report" className="w-full" width={300} height={300} />}
            <Link href={`/project/${projectId}/eagleview`} className="italic text-slate-300 block text-center">
              Show Full Report
            </Link>
          </>
        )}
      </div>
    </Widget>
  );
}
