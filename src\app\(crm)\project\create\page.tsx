import { Metadata, ResolvingMetadata } from 'next/types';
import Link from 'next/link';

import { CustomerPicker } from '@/lib/customers';
import { AssignedToField, Field, Form, FormButton } from '@/lib/form';
import { ActionFooter } from '@/lib/nav';
import { Widget } from '@/lib/widget';
import { PROJECTCATEGORY, PROJECTPRIORITY, PROJECTTYPES, PROJECTSUBTYPES } from '@/schemas';
import PlaceAutocomplete from '@/lib/address/autocomplete';

import { create } from './actions';
import { LocationField } from './fields';
import { getData } from './helpers';

export async function generateMetadata(_: unknown, parent: ResolvingMetadata): Promise<Metadata> {
  const parentMetadata = await parent;

  return {
    title: 'Create | ' + parentMetadata.title?.absolute,
  };
}

export default async function CreatePage({ searchParams }: { searchParams?: { customer?: string } }) {
  // Get customer ID from search params if available
  const customerId = searchParams?.customer;

  // Pass customerId to getData to fetch customer details
  const { googleApiKey, leadSources, customer } = await getData(customerId);

  return (
    <Form className="flex flex-col gap-6" action={create}>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Widget label="Project Information" rowSpan={2}>
          <div className="grid grid-cols-1 gap-4">
            <Field
              required
              name="jobPriority"
              label="Job Priority"
              type="radio"
              options={Object.values(PROJECTPRIORITY)}
            />

            <Field
              required
              name="jobCategory"
              label="Job Category"
              type="radio"
              options={Object.values(PROJECTCATEGORY)}
            />

            <Field required name="jobType" label="Job Type" type="radio" options={Object.values(PROJECTTYPES)} />

            <Field name="jobSubType" label="Job Subtype" type="radio" options={Object.values(PROJECTSUBTYPES)} />

            <Field
              required
              name="leadSource"
              label="Lead Source"
              type="select"
              options={leadSources.map((leadSource) => leadSource.name)}
            />

            <AssignedToField useMention={true} />

            <Field name="companyName" label="Company Name" />

            <LocationField />

            <PlaceAutocomplete apiKey={googleApiKey} name="address" />
          </div>
        </Widget>

        <div className="flex flex-col gap-6">
          {/* Pass the customerId if we have one from the URL */}
          {customerId ? (
            <div>
              <Widget label="Selected Customer">
                <div className="p-4">
                  {customer ? (
                    <div className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-300">
                          <svg
                            className="w-6 h-6"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fillRule="evenodd"
                              d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                        <div>
                          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                            {customer.firstName} {customer.lastName}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {customer.phoneNumbers && customer.phoneNumbers[0] && (
                              <span className="mr-3">{customer.phoneNumbers[0]}</span>
                            )}
                            {customer.emailAddresses && customer.emailAddresses[0] && (
                              <span>{customer.emailAddresses[0]}</span>
                            )}
                          </p>
                        </div>
                      </div>

                      <div className="flex">
                        <Link
                          href={`/customer/${customerId}`}
                          className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mr-4"
                        >
                          <svg
                            className="w-4 h-4 mr-1"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                            <path
                              fillRule="evenodd"
                              d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                          View Customer
                        </Link>
                        <Link
                          href={`/customer/${customerId}/edit`}
                          className="inline-flex items-center text-sm font-medium text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300"
                        >
                          <svg
                            className="w-4 h-4 mr-1"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                          </svg>
                          Edit Customer
                        </Link>
                      </div>
                    </div>
                  ) : (
                    <p className="text-sm text-gray-700 dark:text-gray-300 mb-2">
                      A customer has been pre-selected for this project.
                    </p>
                  )}
                  <input type="hidden" name="customerId" value={customerId} />
                </div>
              </Widget>
            </div>
          ) : (
            <CustomerPicker />
          )}

          <Widget label="Initial Appointment">
            <div className="grid grid-cols-1 gap-4">
              <Field name="appointmentTitle" label="Appointment Title" placeholder="Initial Consultation" />
              <Field name="appointmentStart" label="Appointment Date/Time" type="datetime-local" step={0} />
              <Field name="appointmentDescription" label="Description" placeholder="Appointment details..." />
            </div>
          </Widget>
        </div>
      </div>

      <ActionFooter left={<Link href="/project">Back to Projects</Link>} right={<FormButton>Save</FormButton>} />
    </Form>
  );
}
