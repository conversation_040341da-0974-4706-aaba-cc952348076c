import { cache } from 'react';

import { getProject } from '@/server/projects';
import { getLeadSources } from '@/server/lead-source';

import { edit } from './actions';
import { validatePermissions } from './permissions';

export const getData = cache(async function (id: string) {
  'use server';
  const { data } = await getProject(id);
  const props = await validatePermissions(data);
  const { data: leadSources } = await getLeadSources(props.account._id.toString());

  const editAction = edit.bind(null, id);

  return { ...props, leadSources, editAction };
});
