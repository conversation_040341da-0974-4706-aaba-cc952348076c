'use client';
import { StepBack, StepForward } from 'lucide-react';
import { useFormStatus } from 'react-dom';

import { Form, Spinner } from '@/lib/form';

import type { ManualProps, IContentsProps } from './types';

export function Advance(props: Readonly<ManualProps>) {
  return (
    <Form action={props.action}>
      <Contents before title="Proceed">
        <StepForward className="w-6 h-6" />
      </Contents>
    </Form>
  );
}

export function Recede(props: Readonly<ManualProps>) {
  return (
    <Form action={props.action}>
      <Contents title="Back">
        <StepBack className="w-6 h-6" />
      </Contents>
    </Form>
  );
}

function Contents({ children, before, title }: Readonly<IContentsProps>) {
  const { pending } = useFormStatus();

  return (
    <div className="flex gap-2 items-center">
      {before && pending && <Spinner height="h-4" />}
      <button title={title}>{children}</button>
      {!before && pending && <Spinner height="h-4" />}
    </div>
  );
}
