import Link from 'next/link';

import { formatDate, formatPhoneNumber } from '@/lib/formatting';
import { SCHEMAS, translateModelToRoute } from '@/lib/schemas';
import { ListScreen } from '@/lib/screen';
import type { CustomerDoc } from '@/schemas';
import { validateRequest } from '@/server/auth';
import { TableCell, TableHead } from '@/components/ui/table';

interface CustomerProps {
  searchParams: {
    page?: string | undefined;
  };
}

export default async function ListCustomers({ searchParams }: Readonly<CustomerProps>) {
  const { user } = await validateRequest();

  return (
    <ListScreen<CustomerDoc>
      {...{ searchParams }}
      model={SCHEMAS.CUSTOMER}
      label="Customers"
      description="A complete list of your customers. Keep track of their information and manage your customer base efficiently"
      heading={
        <>
          <TableHead>Name</TableHead>
          <TableHead>Email</TableHead>
          <TableHead>Phone</TableHead>
          <TableHead>Created</TableHead>
        </>
      }
      body={async (item) => {
        const viewUrl = translateModelToRoute(SCHEMAS.CUSTOMER, item.id);

        return (
          <>
            <TableCell scope="row">
              <Link href={viewUrl} className="hover:underline">
                {item.name}
              </Link>
            </TableCell>

            <TableCell>{item.email}</TableCell>
            <TableCell>{formatPhoneNumber(item.phone)}</TableCell>
            <TableCell>{formatDate(item.created, true, user?.tzoffset)}</TableCell>
          </>
        );
      }}
    />
  );
}
