import { revalidatePath } from 'next/cache';

import { db } from '@/lib';
import { ActionResult } from '@/lib/form';
import { assertString } from '@/lib/validators';
import { getProject } from '@/server/projects';

export async function edit(id: string, _: unknown, formData: FormData): Promise<ActionResult> {
  'use server';
  const warnings: string[] = [];

  try {
    await db();

    const names = formData.getAll('name[]');
    const values = formData.getAll('value[]');
    const units = formData.getAll('unit[]');

    const { data: project } = await getProject(id);
    if (!project) throw new Error('Not found: no project found.');

    if (project.isDead) throw new Error('Project is dead');

    const dimensions = names
      .map((name, i) => ({
        name,
        value: values[i],
        unit: units[i],
      }))
      .filter((dimension) => !Object.values(dimension).every((x) => !x));

    project.set('dimensions', dimensions);
    const willDelete = formData.get('_delete');

    if (willDelete && assertString(willDelete)) {
      project.set(
        'dimensions',
        project.dimensions.filter((dimension) => {
          return dimension.name !== willDelete;
        }),
      );
    }

    await project.save({ timestamps: false });
  } catch (e) {
    if (e instanceof Error) return { error: e.message, warnings };

    return { error: 'Unknown error', warnings };
  }

  if (!warnings.length) revalidatePath(`/project/${id}/dimensions`);

  return { error: null, warnings };
}
