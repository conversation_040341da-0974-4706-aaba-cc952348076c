import { Schema } from 'mongoose';

import { SCHEMAS } from '@/lib/schemas';
import { SchemaFactory } from '@/schemas/schema-factory';

import { AutomationMiddleware } from './middleware';
import { Automation, AUTOMATION_ACTION_TYPE, AUTOMATION_TRIGGER_TYPE } from './types';
import { migrationsV1 } from './migrations/v1';

export const AutomationSchema = new SchemaFactory<Automation>(
  SCHEMAS.AUTOMATION,
  {
    _id: { type: Schema.Types.ObjectId, auto: true, required: true },
    account: { type: Schema.Types.ObjectId, required: true },
    name: { type: String, required: true, trim: true },
    description: { type: String, trim: true },
    isActive: { type: Boolean, default: true },
    trigger: {
      type: {
        type: String,
        enum: Object.values(AUTOMATION_TRIGGER_TYPE),
        required: true,
      },
      conditions: { type: Schema.Types.Mixed },
    },
    actions: [
      {
        type: {
          type: String,
          enum: Object.values(AUTOMATION_ACTION_TYPE),
          required: true,
        },
        config: { type: Schema.Types.Mixed, required: true },
      },
    ],
    createdBy: { type: Schema.Types.ObjectId, required: true },
    modifiedBy: { type: Schema.Types.ObjectId, required: true },
  },
  {
    collection: 'automation',
    insertIntoSearchCollection: true,
  },
);

AutomationSchema.migrate(async () => {
  for (const migration of migrationsV1) {
    await migration.up();
  }
});
AutomationSchema.middleware((schema) => {
  AutomationMiddleware(schema);
});
