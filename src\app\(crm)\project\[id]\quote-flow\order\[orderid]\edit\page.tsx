import Link from 'next/link';

import { Action, Field, Form, FormButton } from '@/lib/form';
import { ActionFooter } from '@/lib/nav';
import { QuoteMutateForm } from '@/lib/quote/mutate';
import { Widget } from '@/lib/widget';
import { PROJECT_ORDER_STATUS_OPTIONS } from '@/schemas/projects/subdocuments';

import { getData } from './helpers';
import { EditOrderpageProps } from './types';

export default async function QuoteFlowOrderEdit({ params: { id, orderid } }: Readonly<EditOrderpageProps>) {
  const { order, canDelete, editAction, canEditOrder } = await getData(id, orderid);
  const { quotes } = order;

  return (
    <Form action={editAction} className="flex flex-col gap-6">
      <Widget label="Order" className="space-y-4">
        <Field type="text" name="name" value={order.name} label="Name" />
        <Field
          type="select"
          name="status"
          value={order.status}
          label="Status"
          readOnly={!canEditOrder}
          options={PROJECT_ORDER_STATUS_OPTIONS}
        />
      </Widget>

      {quotes.map((quote) => (
        <QuoteMutateForm
          namePrefix={quote._id.toString()}
          key={quote._id.toString()}
          label={`${quote.name} Section`}
          canDelete={canDelete}
          quote={quote}
          excludeLocation
        />
      ))}

      <ActionFooter
        left={<Link href={`/project/${id}/quote-flow/order/${orderid}`}>Back to Order</Link>}
        right={
          <>
            <FormButton>Save</FormButton>
            {canDelete && <Action />}
          </>
        }
      />
    </Form>
  );
}
