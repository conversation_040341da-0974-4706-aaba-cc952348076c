import { cache } from 'react';

import { getLeadSource } from '@/server/lead-source';

import { edit } from './actions';
import { validatePermissions } from './permissions';

export const getData = cache(async function (id: string) {
  'use server';
  const { data } = await getLeadSource(id);
  const { canEdit, canDelete, user, account, leadSource } = await validatePermissions(data);
  const editAction = edit.bind(null, id, canDelete);

  return { canEdit, canDelete, user, account, leadSource, editAction };
});
