import { cache } from 'react';

import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';
import { can } from '@/lib/capabilities';

export const validatePermissions = cache(async function (id: string) {
  'use server';
  const { user, account, isSystemAdmin } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not edit account settings.');

  let canEdit = await can(user, 'edit_account');
  canEdit = (account._id.equals(id) && canEdit) || isSystemAdmin;
  if (!canEdit) throw new ServerError('Forbidden: you can not edit account settings.');

  const canDelete = (await can(user, 'delete_account')) && !account._id.equals(id);

  return { canEdit, canDelete, user, account };
});
