import { Metadata, ResolvingMetadata } from 'next';

import { WidgetEdit, WithTests } from '@/lib/account';
import { Field, Form, SecretField } from '@/lib/form';
import { Card, CardContent } from '@/components/ui/card';

import { getData } from './helpers';
import { TestConnection } from './tests';
import { EditPageProps } from './types';

export async function generateMetadata(_: unknown, parent: ResolvingMetadata): Promise<Metadata> {
  const parentMetadata = await parent;

  return {
    title: 'Beacon | ' + parentMetadata.title?.absolute,
  };
}

export default async function EditPage({ params }: Readonly<EditPageProps>) {
  const { account, editAction, secrets } = await getData(params.id);

  return (
    <Form action={editAction}>
      <WidgetEdit label={account.name} id={account.id}>
        <WithTests canTest={true} tests={<TestConnection />}>
          <Card>
            <CardContent className="pt-4">
              <div className="space-y-4">
                <Field name="profileId" label="Profile ID" type="text" value={account.settings?.beacon?.profileId} />
                <Field name="username" label="Username" type="text" value={account.settings?.beacon?.username} />

                <SecretField isSet={!!secrets?.settings?.beacon?.password} label="Edit Password">
                  <Field autoComplete="off" name="password" label="Password" type="password" />
                </SecretField>
              </div>
            </CardContent>
          </Card>
        </WithTests>
      </WidgetEdit>
    </Form>
  );
}
