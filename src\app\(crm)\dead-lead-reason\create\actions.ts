'use server';

import { redirect } from 'next/navigation';

import { db } from '@/lib';
import { ActionResult } from '@/lib/form';
import { getDeadLeadReasonModel } from '@/schemas';

import { validatePermissions } from './permissions';

export async function create(_: unknown, formData: FormData): Promise<ActionResult> {
  let _id = null;

  try {
    await db();
    await validatePermissions();

    const DeadLeadReasonModel = await getDeadLeadReasonModel();
    const deadLeadReason = new DeadLeadReasonModel({
      name: formData.get('name'),
    });

    const response = await deadLeadReason.save();

    _id = response?._id;
  } catch (error) {
    if (error instanceof Error) return { error: error.message };

    return { error: 'Unknown error' };
  }

  if (!_id) return { error: 'Failed to create record' };

  redirect(`/dead-lead-reason/${_id}`);
}
