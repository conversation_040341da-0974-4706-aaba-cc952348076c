'use client';

import { useState } from 'react';

import { Field, FormButton, Button } from '@/lib/form';
import { Modal } from '@/lib/modal';

import { KillProps } from './types';

export function Kill(props: Readonly<KillProps>) {
  const [isOpen, setIsOpen] = useState(false);

  const handleOpen = () => setIsOpen(true);
  const handleClose = () => setIsOpen(false);

  // Check if this is for making a project dead/cancelled or making it alive
  const isForMakingDead = props.title.toLowerCase().includes('mark as');

  return (
    <>
      <div className={props.className} title={props.title} onClick={handleOpen}>
        {props.children}
      </div>

      {isOpen && (
        <Modal
          label={props.title}
          onClose={handleClose}
          action={props.action}
          actions={[
            <Button key="cancel" onClick={handleClose}>
              Cancel
            </Button>,
            <FormButton key="submit">Submit</FormButton>,
          ]}
        >
          {isForMakingDead ? (
            <div className="flex flex-col gap-4 p-4">
              <p className="text-sm">Please select a reason for {props.title.toLowerCase()}.</p>
              <Field
                name="leadDeadReason"
                label="Reason"
                type="select"
                required
                options={props.deadLeadReasons.map((reason) => ({
                  label: reason.name,
                  value: reason.name,
                }))}
              />
            </div>
          ) : (
            <div className="flex flex-col gap-4 p-4">
              <p className="text-sm">Are you sure you want to {props.title.toLowerCase()}?</p>
            </div>
          )}
        </Modal>
      )}
    </>
  );
}
