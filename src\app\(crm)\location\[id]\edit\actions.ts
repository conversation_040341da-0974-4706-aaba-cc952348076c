import mongoose from 'mongoose';
import { redirect } from 'next/navigation';

import { db } from '@/lib';
import { ActionResult } from '@/lib/form';
import { assertString } from '@/lib/validators';
import { getLocationModel } from '@/schemas';
import { formatAddressForSave } from '@/server/addresses';

export async function edit(
  id: string,
  canDelete: boolean,
  canInputCoords: boolean,
  _: unknown,
  formData: FormData,
): Promise<ActionResult> {
  'use server';
  const { _action } = Object.fromEntries(formData);

  if (_action === 'delete') {
    if (!canDelete) return { error: 'Forbidden: you do not have permissions to delete this location.' };

    try {
      await db();

      const locationModel = await getLocationModel();
      await locationModel.deleteOne({ _id: new mongoose.mongo.ObjectId(id) });
    } catch (e) {
      if (e instanceof Error) return { error: e.message };

      return { error: 'Unknown error' };
    }

    redirect(`/location`);
  }

  try {
    await db();

    const locationModel = await getLocationModel();
    const document = await locationModel.findOne({ _id: new mongoose.mongo.ObjectId(id) });
    if (!document) throw new Error('Not Found');

    const name = formData.get('name');
    if (assertString(name)) document.name = name;

    document.address = formatAddressForSave({ formData, setCoordinates: canInputCoords });

    await document.save();
  } catch (e) {
    if (e instanceof Error) return { error: e.message };

    return { error: 'Unknown error' };
  }

  redirect(`/location/${id}`);
}
