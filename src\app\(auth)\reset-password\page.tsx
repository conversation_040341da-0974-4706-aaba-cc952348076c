import Link from 'next/link';

import { <PERSON>, Wrapper } from '@/lib/auth';
import { FormButton, Passwords } from '@/lib/form';

import { getData } from './helpers';
import { Origin } from './origin';
import { resetPassword, setPassword } from './actions';

export default async function RestPassword({ searchParams }: Readonly<{ searchParams: { confirm?: string } }>) {
  const { isConfirming } = await getData(searchParams.confirm);

  return (
    <Wrapper
      title="Reset Password"
      description={isConfirming ? '' : 'Enter your email below to reset your password'}
      action={isConfirming ? setPassword : resetPassword}
    >
      {isConfirming ? (
        <>
          <Passwords required />
          <input type="hidden" name="_confirm" value={searchParams.confirm} />
        </>
      ) : (
        <Field name="email" label="Email" placeholder="Enter your email" type="email" required autoComplete="email" />
      )}

      <Origin />

      <FormButton className="w-full">Reset Password</FormButton>

      <p className="text-center">
        <Link className="text-primary hover:underline cursor-pointer" href="/login">
          login
        </Link>
      </p>
    </Wrapper>
  );
}
