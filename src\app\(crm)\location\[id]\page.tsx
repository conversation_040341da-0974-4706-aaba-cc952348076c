import Link from 'next/link';

import { DisplayData } from '@/lib/view';
import { EditIcon, Widget } from '@/lib/widget';
import { ActionFooter } from '@/lib/nav';

import { getData } from './helpers';
import { LocationViewProps } from './types';

export default async function ViewLocation({ params }: Readonly<LocationViewProps>) {
  const { location, canEdit, account } = await getData(params.id);
  const editLink = `/location/${location._id}/edit`;

  return (
    <div className="space-y-6">
      <Widget label={location.name} action={canEdit && <EditIcon href={editLink} />}>
        <DisplayData label="Name" value={location.name} />
        <DisplayData label="Address" value={location.address?.full} />
      </Widget>

      <ActionFooter left={<Link href={`/account/${account._id.toString()}/custom-fields`}>Back to Locations</Link>} />
    </div>
  );
}
