import Link from 'next/link';
import type { Metadata, ResolvingMetadata } from 'next/types';

import { ActionFooter } from '@/lib/nav';
import { OrderViewPage } from '@/lib/quote/view/order';
import { DisplayData } from '@/lib/view';
import { EditIcon, Widget } from '@/lib/widget';
import { formatCurrency, formatDate } from '@/lib/formatting';
import { Form, FormButton } from '@/lib/form';
import { Origin } from '@/lib/form/origin';
import { PROJECT_ORDER_STATUS_DICTIONARY, SIGNATURE_TYPE } from '@/schemas/projects/subdocuments';

import { getData, getSignatureName } from './helpers';
import type { ProjectOrderViewProps, SignatureBlockProps } from './types';
import { sign } from './actions';

export async function generateMetadata(
  props: Readonly<ProjectOrderViewProps>,
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const parentMetadata = await parent;
  const { order } = await getData(props.params.id, props.params.orderid);

  return {
    title: `${order.name} | Order | ` + parentMetadata.title?.absolute,
  };
}

export default async function ProjectOrderView(props: Readonly<ProjectOrderViewProps>) {
  const { id: projectId, orderid: orderId } = props.params;

  const {
    order,
    canEdit,
    user,
    canScheduleDeliveryEvents,
    canScheduleLaborEvents,
    deliveryEvents,
    laborEvents,
    // customerSigned,
    project,
    sendSignEmailToCustomerAction,
  } = await getData(projectId, orderId);
  const { quotes } = order;

  return (
    <div className="space-y-6">
      <Widget
        label="Order"
        action={
          <div className="flex gap-4 items-center flex-row-reverse">
            {canEdit && <EditIcon href={`/project/${projectId}/quote-flow/order/${orderId}/edit`} />}
            <Form action={sendSignEmailToCustomerAction}>
              <Origin />
              <FormButton disabled={true}>Send Email to Customer</FormButton>
            </Form>
          </div>
        }
      >
        <DisplayData label="Name" value={order.name} />
        <DisplayData label="Status" value={PROJECT_ORDER_STATUS_DICTIONARY[order.status]} />
        <DisplayData label="Original Quoted Total" value={formatCurrency(order.originalQuoteTotal)} />
        <DisplayData label="Original Subtotal" value={formatCurrency(order.subtotalWithoutProfit)} />
        <DisplayData label="Profit Margin" value={formatCurrency(order.profitMargin)} />
        <DisplayData label="Subtotal" value={formatCurrency(order.subtotal)} />
        <DisplayData label="Discount" value={formatCurrency(order.discount)} />
        <DisplayData label={`Tax (${order.taxPercentage}%)`} value={formatCurrency(order.tax)} />
        <DisplayData label="Total" value={formatCurrency(order.total)} />
        <DisplayData label="Commission" value={formatCurrency(order.commission)} />
      </Widget>

      {quotes.map((quote) => (
        <OrderViewPage
          quote={quote}
          key={quote._id.toString()}
          readOnly={order.readOnly}
          projectId={projectId}
          orderId={orderId}
          tzoffset={user.tzoffset ?? 0}
          location={project.documents?.location}
          canScheduleDeliveryEvents={canScheduleDeliveryEvents}
          canScheduleLaborEvents={canScheduleLaborEvents}
          deliveryEvents={deliveryEvents}
          laborEvents={laborEvents}
        />
      ))}

      <div className="mt-6 flex flex-col gap-4">
        <p>
          <Link
            className="text-orange-500 hover:underline cursor-pointer"
            href={`/project/${projectId}/order/${orderId}/sign`}
          >
            Go Customer Signature Page
          </Link>
        </p>
        <SignatureBlock type={SIGNATURE_TYPE.PM} projectId={projectId} orderId={orderId} />
        <SignatureBlock type={SIGNATURE_TYPE.ADMIN} projectId={projectId} orderId={orderId} />
      </div>

      <ActionFooter left={<Link href={`/project/${projectId}/quote-flow`}>Back to Quote Flow</Link>} />
    </div>
  );
}

async function SignatureBlock(props: Readonly<SignatureBlockProps>) {
  const { canSignAsAdmin, canSignWithCustomerInperson, canSignAsPm, order } = await getData(
    props.projectId,
    props.orderId,
  );
  const signatures = order.signatures;

  const existingSignature = signatures.find((sign) => sign.type === props.type);
  const isSigned = !!existingSignature;
  const name = await getSignatureName(props.type, props.projectId, props.orderId);
  const action = sign.bind(null, props.type, props.projectId, props.orderId);

  let canSign = true;

  if (props.type === SIGNATURE_TYPE.CUSTOMER && !canSignWithCustomerInperson) {
    canSign = false;
  } else if (props.type === SIGNATURE_TYPE.ADMIN && !canSignAsAdmin) {
    canSign = false;
  } else if (props.type === SIGNATURE_TYPE.PM && !canSignAsPm) {
    canSign = false;
  }

  let label = '';
  if (props.type === SIGNATURE_TYPE.CUSTOMER) label = 'Customer';
  if (props.type === SIGNATURE_TYPE.ADMIN) label = 'Admin Approval';
  if (props.type === SIGNATURE_TYPE.PM) label = 'Project Manager Approval';

  const signatureContainerClassList = [
    'lg:whitespace-nowrap',
    'lg:flex',
    'gap-4',
    'items-center',
    'rounded',
    'px-6',
    'py-3',
    'text-lg',
    'font-bold',
  ];

  if (isSigned) {
    signatureContainerClassList.push('bg-green-700', 'text-white');
  } else {
    signatureContainerClassList.push('bg-white', 'text-black');
  }

  return (
    <Form className="select-none grid grid-cols-4 gap-4 items-center" action={action}>
      <div className="col-span-3">
        <div className={signatureContainerClassList.join(' ')}>
          <div>{label}:</div>

          {isSigned ? (
            <div className="border-b-2 border-black w-full lg:flex gap-4 items-center">
              <div className="font-cursive w-full">{name}</div>
              <div>Date:</div>
              <div>{formatDate(existingSignature.created, true)}</div>
            </div>
          ) : (
            <div className="w-full border-b-2 border-black">&nbsp;</div>
          )}
        </div>
      </div>
      <div>
        <FormButton disabled={isSigned || !canSign}>Approve</FormButton>
      </div>
    </Form>
  );
}
