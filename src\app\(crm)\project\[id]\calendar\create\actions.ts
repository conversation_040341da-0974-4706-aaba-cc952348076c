'use server';

import mongoose from 'mongoose';
import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

import { db } from '@/lib';
import { getCalendarEventModel, getProjectModel } from '@/schemas';
import { ActionResult } from '@/lib/form';
import { validateRequest } from '@/server/auth';
import { ServerError } from '@/server/error';
import { CALENDAR_EVENT_TYPES } from '@/lib/calendar/constants';
import { checkAvailableCrew } from '@/lib/calendar/actions';
import { getStartAndEndDates } from '@/lib/calendar/helpers';
import { convertFromUserTz } from '@/lib/timezone';
import { PROJECTACTIVITYTYPES } from '@/schemas/projects/subdocuments/activity-history/enums';

interface MaterialDelivery {
  projectId: string;
  orderId: string;
}

interface LaborStart {
  projectId: string;
  orderId: string;
}

export async function createMaterialDelivery(
  { projectId, orderId }: MaterialDelivery,
  _: unknown,
  formData: FormData,
): Promise<ActionResult> {
  await db();

  const { user } = await validateRequest();

  if (!user) throw new ServerError('User not found');
  const userTz = user?.tzoffset ?? 0;
  const projectModel = await getProjectModel();
  const project = await projectModel.findById(projectId);
  if (!project) throw new Error('Project not found');

  const title = formData.get('title') + '';
  const start = convertFromUserTz(new Date(formData.get('start') + ''), userTz);
  const time = formData.get('time') + '';
  const description = formData.get('description') + '';
  const procurementMethod = formData.get('procurementMethod') + '';
  const deliveryMethod = formData.get('deliveryMethod') + '';
  const type = CALENDAR_EVENT_TYPES.DELIVERY;

  const order = project.orders.find((o) => o._id.toString() === orderId);
  if (!order) throw new Error('Order not found');

  try {
    const eventId = new mongoose.Types.ObjectId();
    project.deliveryEvents.push({
      _id: eventId,
      orderId,
      title,
      type,
      start,
      time,
      procurementMethod,
      deliveryMethod,
      description,
      createdBy: user._id,
      modifiedBy: user._id,
    });

    // Add activity history entry for delivery creation
    project.activityHistory.push({
      _id: new mongoose.Types.ObjectId(),
      type: PROJECTACTIVITYTYPES.FILEUPLOAD, // Using an existing valid activity type
      user: user._id,
      value: `Delivery event "${title}" created`,
      created: new Date(),
    });

    await project.save();
  } catch (error) {
    if (error instanceof Error) {
      return { error: error.message };
    }

    return { error: 'Unknown error' };
  }

  revalidatePath(`/project/${projectId}`);
  revalidatePath(`/project/${projectId}/calendar`);

  redirect(`/project/${projectId}/calendar`);
}

export async function createLabor(
  { projectId, orderId }: LaborStart,
  _: unknown,
  formData: FormData,
): Promise<ActionResult> {
  await db();

  const { user } = await validateRequest();

  if (!user) throw new ServerError('User not found');
  const userTz = user?.tzoffset ?? 0;

  const projectModel = await getProjectModel();
  const calendarEventModel = await getCalendarEventModel();

  const project = await projectModel.findById(projectId);
  if (!project) throw new Error('Project not found');

  const order = project.orders.find((o) => o._id.toString() === orderId);
  if (!order) throw new Error('Order not found');

  const title = formData.get('title') + '';
  const description = formData.get('description') + '';
  const crew = formData.get('crew');
  const type = CALENDAR_EVENT_TYPES.LABOR;
  const start = convertFromUserTz(new Date(formData.get('start') + ''), 0);
  const end = convertFromUserTz(new Date(formData.get('end') + ''), 0);

  if (!crew) {
    throw new Error('Labor must be assigned to a crew');
  }

  const { startDate, endDate } = getStartAndEndDates(start, end, userTz);

  try {
    await checkAvailableCrew(crew.toString(), startDate, endDate);

    const calendarEvent = await calendarEventModel.create({
      project,
      crew,
      title,
      start: startDate,
      end: endDate,
      type,
      createdBy: user._id,
      modifiedBy: user._id,
    });

    const eventId = new mongoose.Types.ObjectId();
    project.laborEvents.push({
      _id: eventId,
      orderId,
      title,
      type,
      description,
      event: calendarEvent._id,
      createdBy: user._id,
      modifiedBy: user._id,
      crew,
    });

    // Add activity history entry for labor creation
    project.activityHistory.push({
      _id: new mongoose.Types.ObjectId(),
      type: PROJECTACTIVITYTYPES.FILEUPLOAD, // Using an existing valid activity type
      user: user._id,
      value: `Labor event "${title}" created for crew`,
      created: new Date(),
    });

    await project.save();
  } catch (error) {
    if (error instanceof Error) {
      return { error: error.message };
    }

    return { error: 'Unknown error' };
  }

  revalidatePath(`/project/${projectId}`);
  revalidatePath(`/project/${projectId}/calendar`);

  redirect(`/project/${projectId}/calendar`);
}

export async function createAppointment(projectId: string, _: unknown, formData: FormData): Promise<ActionResult> {
  await db();

  const { user } = await validateRequest();
  const userTz = user?.tzoffset ?? 0;

  if (!user) throw new ServerError('User not found');

  const projectModel = await getProjectModel();
  const project = await projectModel.findById(projectId);
  if (!project) throw new Error('Project not found');

  const title = formData.get('title') + '';

  const start = convertFromUserTz(new Date(formData.get('start') + ''), userTz);
  const end = convertFromUserTz(new Date(formData.get('end') + ''), userTz);

  const type = CALENDAR_EVENT_TYPES.APPOINTMENT;
  const description = formData.get('description') + '';
  const assignedTo = formData.get('assignedTo');

  if (!assignedTo) {
    throw new Error('Appointment must be assigned to a user');
  }

  try {
    const eventId = new mongoose.Types.ObjectId();
    project.appointments.push({
      _id: eventId,
      title,
      start,
      end,
      type,
      description,
      assignedTo,
      createdBy: user._id,
      modifiedBy: user._id,
    });

    // Add activity history entry for appointment creation
    project.activityHistory.push({
      _id: new mongoose.Types.ObjectId(),
      type: PROJECTACTIVITYTYPES.FILEUPLOAD, // Using an existing valid activity type
      user: user._id,
      value: `Appointment "${title}" created`,
      created: new Date(),
    });

    await project.save();
  } catch (error) {
    if (error instanceof Error) {
      return { error: error.message };
    }

    return { error: 'Unknown error' };
  }

  revalidatePath(`/project/${projectId}`);
  revalidatePath(`/project/${projectId}/calendar`);

  redirect(`/project/${projectId}/calendar`);
}
