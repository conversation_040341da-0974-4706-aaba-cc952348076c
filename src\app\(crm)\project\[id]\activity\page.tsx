import Link from 'next/link';

import { formatDate } from '@/lib/formatting';
import { ActionFooter, Pagination } from '@/lib/nav';
import { Body, Cell, Heading, Row, Table } from '@/lib/table';
import { Widget } from '@/lib/widget';
import type { ProjectDoc } from '@/schemas';

import { getData } from './helpers';
import { ProjectActivityProps } from './types';

export default async function ListProjectOrders(props: ProjectActivityProps) {
  const {
    params: { id },
  } = props;

  const { project, canViewUsers } = await getData(id);
  const activityHistory = project.activityHistory;

  return (
    <div className="flex flex-col gap-6">
      <Widget label="Activity" footer={<Pagination total={1} page={1} />}>
        <Table>
          <Heading>
            <Cell>Message</Cell>
            <Cell>User</Cell>
            <Cell>Created</Cell>
          </Heading>
          <Body>
            {activityHistory.map(async (activity) => {
              return <ActivityRow key={activity.id} activity={activity} canViewUsers={canViewUsers} />;
            })}
          </Body>
        </Table>
      </Widget>

      <ActionFooter left={<Link href={`/project/${id}`}>Back to Project</Link>} />
    </div>
  );
}

async function ActivityRow({
  activity,
  canViewUsers,
}: Readonly<{ canViewUsers: boolean; activity: ProjectDoc['activityHistory'][number] }>) {
  const user = activity.documents?.user;
  let userLinkRender: React.ReactNode = 'System';

  if (user) {
    if (canViewUsers) {
      userLinkRender = (
        <Link href={`/user/${user._id}`} className="hover:no-underline underline">
          {user.name}
        </Link>
      );
    } else {
      userLinkRender = user.name;
    }
  }

  return (
    <Row>
      <Cell>{activity.value}</Cell>
      <Cell>{userLinkRender}</Cell>
      <Cell>{formatDate(activity.created, true)}</Cell>
    </Row>
  );
}
