import Link from 'next/link';
import { Metadata, ResolvingMetadata } from 'next/types';

import { QuoteViewPage } from '@/lib/quote/view';

import { getData } from './helpers';
import { ProjectQuoteViewProps } from './types';

export async function generateMetadata(props: ProjectQuoteViewProps, parent: ResolvingMetadata): Promise<Metadata> {
  const {
    params: { id, quoteid },
  } = props;

  const parentMetadata = await parent;
  const { quote } = await getData(id, quoteid);

  return {
    title: [quote.name, parentMetadata.title?.absolute].join(' | '),
  };
}

export default async function ProjectQuoteViewPage(props: Readonly<ProjectQuoteViewProps>) {
  const {
    params: { id, quoteid },
  } = props;
  const { quote, canEdit, project } = await getData(id, quoteid);

  const editLink = `/project/${id}/quote-flow/${quoteid}/edit`;

  return (
    <QuoteViewPage
      quote={quote}
      editLink={canEdit ? editLink : undefined}
      readOnly={project.readOnly}
      footer={
        <div>
          <Link href={`/project/${id}/quote-flow`}>Back to Quote Flow</Link>
        </div>
      }
    />
  );
}
