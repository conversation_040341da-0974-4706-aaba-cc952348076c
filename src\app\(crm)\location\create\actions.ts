import { redirect } from 'next/navigation';

import { db } from '@/lib';
import { ActionResult } from '@/lib/form';
import { getLocationModel } from '@/schemas';
import { formatAddressForSave } from '@/server/addresses';

import { validatePermissions } from './permissions';

export async function create(_: unknown, formData: FormData): Promise<ActionResult> {
  'use server';
  let _id = null;
  let canInputCoords = false;

  try {
    ({ canInputCoords } = await validatePermissions());
  } catch (error) {
    if (error instanceof Error) return { error: error.message };

    return { error: 'Unknown error' };
  }

  try {
    await db();
    const locationModel = await getLocationModel();
    const response = await locationModel.create({
      name: formData.get('name'),
      address: formatAddressForSave({
        formData,
        setCoordinates: canInputCoords,
      }),
    });

    _id = response._id;
  } catch (e) {
    if (e instanceof Error) return { error: e.message };

    return { error: 'Unknown error' };
  }

  if (!_id) return { error: 'Failed to create record' };

  redirect(`/location/${_id}`);
}
