import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function () {
  'use server';
  const { user, account } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not create insurance companies.');

  const canCreate = await can(user, 'create_insurancecompany');
  if (!canCreate) throw new ServerError('Forbidden: you can not create insurance companies.');

  return { canCreate, user, account };
});
