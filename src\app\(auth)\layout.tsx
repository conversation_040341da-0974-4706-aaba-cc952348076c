import type { Metadata } from 'next';
import { redirect } from 'next/navigation';
// import Link from 'next/link';

import { validateRequest } from '@/server/auth';
// import { getAccountFromUrl } from '@/server/accounts';

// import background from './auth-background.webp';

export const metadata: Metadata = {
  title: 'Auth | Trussi.ai',
  description: 'Trussi.ai',
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const { user } = await validateRequest();
  if (user?.verified) {
    redirect('/');
  }

  // let backgroundimage = background.src;

  try {
    // const { data: account } = await getAccountFromUrl();
    // if (account?.settings?.authBackground) backgroundimage = '/file/' + account.settings.authBackground.toString();
  } catch {}

  return (
    <div className="flex min-h-svh flex-col items-center justify-center gap-6 bg-muted p-6 md:p-10">
      <div className="flex w-full max-w-sm flex-col gap-6">
        {/* <Link href='/login' className="flex items-center gap-2 self-center font-medium"> */}
        {/* <div className="flex h-6 w-6 items-center justify-center rounded-md bg-primary text-primary-foreground"> */}
        {/* <GalleryVerticalEnd className="size-4" /> */}
        {/* </div> */}
        {/* Trussi.ai */}
        {/* </Link> */}
        {children}
      </div>
    </div>

    // <div className="h-screen bg-cover bg-center" style={{ backgroundImage: `url("${backgroundimage}")` }}>
    //   <div className="flex min-h-full flex-col justify-center px-6 py-12 lg:px-8">{children}</div>
    // </div>
  );
}
