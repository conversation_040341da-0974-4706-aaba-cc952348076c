import { Check, X } from 'lucide-react';

import { formatDate } from '@/lib/formatting';
import { PROJECTMILESTONE } from '@/schemas/projects/subdocuments';
import { validateRequest } from '@/server/auth';

import { MilestoneIconProps } from './types';

export async function Icon(props: Readonly<MilestoneIconProps>) {
  const { symbol, label, milestone, isDead, isCancelled } = props;
  const { user } = await validateRequest();
  const classList = [
    'rounded-full',
    'aspect-square',
    'p-4',
    'w-16',
    'h-16',
    'flex',
    'justify-center',
    'items-center',
    'text-xl',
    'font-bold',
  ];

  const dotClassList = ['rounded-full', 'flex', 'items-center', 'justify-center', 'h-4', 'w-4'];

  // Check if the project is dead or cancelled
  const isDeadOrCancelled = isDead || isCancelled;
  const isDeadStatus = label === PROJECTMILESTONE.DEAD;
  const isCancelledStatus = label === PROJECTMILESTONE.CANCELLED;

  if (milestone && !isDeadOrCancelled) {
    // Normal completed milestone - green
    classList.push('bg-green-600');
    dotClassList.push('bg-green-600');
  } else if (!milestone && !isDeadOrCancelled) {
    // Normal incomplete milestone - blue
    classList.push('bg-gray-100');
    dotClassList.push('bg-gray-100');
  } else if (isDeadOrCancelled) {
    if (isDeadStatus || isCancelledStatus) {
      // Dead or Cancelled status itself - red
      classList.push('bg-red-800');
      dotClassList.push('bg-red-800');
    } else {
      // Other statuses when project is dead/cancelled - gray
      classList.push('bg-gray-100');
      dotClassList.push('bg-gray-100');
    }
  } else {
    classList.push('bg-red-800');
    dotClassList.push('bg-red-800');
  }

  let statusIcon: React.ReactNode = null;

  if (milestone && label !== PROJECTMILESTONE.DEAD && label !== PROJECTMILESTONE.CANCELLED) {
    statusIcon = <Check className="h-2 w-2" />;
  } else if (milestone) {
    statusIcon = <X className="h-2 w-2" />;
  }

  return (
    <div className="flex flex-col gap-4 items-center">
      <div className={classList.join(' ')}>{symbol}</div>
      <div className={dotClassList.join(' ')}>{statusIcon}</div>
      <div className="text-center projecting-none text-xs xl:text-base">
        <div className="font-bold">{label}</div>
        {!!milestone && <div className="text-sm">{formatDate(milestone.date, false, user?.tzoffset)}</div>}
      </div>
    </div>
  );
}
