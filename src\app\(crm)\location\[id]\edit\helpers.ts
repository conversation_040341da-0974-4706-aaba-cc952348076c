import { cache } from 'react';

import { getLocation } from '@/server/locations';

import { edit } from './actions';
import { validatePermissions } from './permissions';

export const getData = cache(async function (id: string) {
  'use server';
  const { data } = await getLocation(id);
  const { canEdit, canDelete, user, account, location, canInputCoords } = await validatePermissions(data);
  const editAction = edit.bind(null, id, canDelete, canInputCoords);

  return { canEdit, canDelete, canInputCoords, user, account, location, editAction };
});
