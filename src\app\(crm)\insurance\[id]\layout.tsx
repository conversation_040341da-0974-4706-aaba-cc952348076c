import { Metadata, ResolvingMetadata } from 'next/types';

import { getData } from './helpers';
import { ViewInsuranceProps } from './types';

export async function generateMetadata({ params }: ViewInsuranceProps, parent: ResolvingMetadata): Promise<Metadata> {
  const { insurance } = await getData(params.id);

  const name = insurance.name || insurance.id;
  const parentMetadata = await parent;
  const title = [name, parentMetadata.title?.absolute].filter((x) => !!x);

  return {
    title: title.join(' | '),
  };
}

export default function InsuranceIdLayout({ children }: Readonly<{ children: React.ReactNode }>) {
  return children;
}
