import mongoose from 'mongoose';
import { revalidatePath } from 'next/cache';

import { db } from '@/lib';
import { currentUserCan } from '@/lib/capabilities';
import { ActionResult } from '@/lib/form';
import { assertString } from '@/lib/validators';
import { getProjectModel, ProjectDoc } from '@/schemas';
import { LASTPIPELINEMILESTONE, PROJECTMILESTONE } from '@/schemas/projects/subdocuments';
import { ServerError } from '@/server';

async function milestoneAction(id: string, callback: (project: ProjectDoc) => Promise<void>): Promise<string> {
  const canEdit = await currentUserCan('edit_project');
  if (!canEdit) throw new ServerError('Forbidden');

  await db();
  const projectModel = await getProjectModel();
  const project = await projectModel.findOne({ _id: new mongoose.mongo.ObjectId(id) });

  if (!project) throw new ServerError('No Project');

  await callback(project);
  await project.save({ timestamps: false });

  return id;
}

export async function advanceMilestone(id: string): Promise<ActionResult> {
  'use server';
  let _id = '';

  try {
    _id = await milestoneAction(id, async function (project) {
      if (project.isDead) throw new Error("Project is dead, can't progress to the next milestone.");

      const lastItem = project.milestones[project.milestones.length - 1];
      let newMilestone = PROJECTMILESTONE.LEAD;

      if (lastItem) {
        if (lastItem.name === LASTPIPELINEMILESTONE) throw new Error(`${LASTPIPELINEMILESTONE} can't be advanced`);

        const possibleMilestones = Object.values(PROJECTMILESTONE);

        let use = false;
        for (const milestone of possibleMilestones) {
          if (use) {
            newMilestone = milestone;
            break;
          }

          if (milestone === lastItem.name) use = true;
        }
      }

      // check if the project is moved from lead to prospect with no PM assigned
      if (
        project.currentMilestone === PROJECTMILESTONE.LEAD &&
        newMilestone === PROJECTMILESTONE.PROSPECT &&
        !project.assignedTo
      ) {
        throw new Error('A PM must be assigned to the project before it can be advanced to the prospect milestone.');
      }

      project.milestones.push({ name: newMilestone });
    });
  } catch (e) {
    if (e instanceof Error) return { error: e.message };
  }

  if (!_id) return { error: 'Unknown error' };
  revalidatePath(`/project/${_id}`);

  return { error: null };
}

export async function recedeMilestone(id: string): Promise<ActionResult> {
  'use server';
  let _id = '';

  try {
    _id = await milestoneAction(id, async function (project) {
      if (project.isDead) throw new Error("Project is dead, can't go back to the previous milestone.");
      project.milestones.$pop();
    });
  } catch (e) {
    if (e instanceof Error) return { error: e.message };
  }

  if (!_id) return { error: 'Unknown error' };
  revalidatePath(`/project/${_id}`);

  return { error: null };
}

export async function deadReason(id: string, _: unknown, formData: FormData): Promise<ActionResult> {
  'use server';
  let _id = '';

  try {
    _id = await milestoneAction(id, async function (project) {
      const reason = formData.get('leadDeadReason');
      if (!assertString(reason)) throw new Error('No reason given');

      const reasonString = reason.trim();
      if (!reasonString) throw new Error('No reason given');

      project.leadDeadReason = reasonString;
    });
  } catch (e) {
    if (e instanceof Error) return { error: e.message };
  }

  if (!_id) return { error: 'Unknown error' };
  revalidatePath(`/project/${_id}`);

  return { error: null };
}

export async function makeDead(id: string, _: unknown, formData: FormData): Promise<ActionResult> {
  'use server';
  let _id = '';

  try {
    // Get the reason from the form data
    const reason = formData.get('leadDeadReason');
    if (!assertString(reason)) return { error: 'A reason must be selected before marking as dead or cancelled.' };

    const reasonString = reason.trim();
    if (!reasonString) return { error: 'A reason must be selected before marking as dead or cancelled.' };

    _id = await milestoneAction(id, async function (project) {
      if (project.isClosed) throw new Error('Project is already closed.');
      if (project.isCancelled) throw new Error('Project is already cancelled.');
      if (project.isDead) throw new Error('Project is already dead.');

      // Set the reason first
      project.leadDeadReason = reasonString;

      // Then update the milestone
      if (project.canBeMadeDead) {
        project.milestones.push({ name: PROJECTMILESTONE.DEAD });
      } else {
        project.milestones.push({ name: PROJECTMILESTONE.CANCELLED });
      }
    });
  } catch (e) {
    if (e instanceof Error) return { error: e.message };
  }

  if (!_id) return { error: 'Unknown error' };
  revalidatePath(`/project/${_id}`);

  return { error: null };
}

export async function makeAlive(id: string): Promise<ActionResult> {
  'use server';
  let _id = '';

  try {
    _id = await milestoneAction(id, async function (project) {
      if (!project.isDead && !project.isCancelled) throw new Error('Project is not dead or cancelled.');

      project.set('leadDeadReason', undefined);
      project.set(
        'milestones',
        project.milestones.filter((x) => x.name !== PROJECTMILESTONE.DEAD && x.name !== PROJECTMILESTONE.CANCELLED),
      );
    });
  } catch (e) {
    if (e instanceof Error) return { error: e.message };
  }

  if (!_id) return { error: 'Unknown error' };
  revalidatePath(`/project/${_id}`);

  return { error: null };
}
