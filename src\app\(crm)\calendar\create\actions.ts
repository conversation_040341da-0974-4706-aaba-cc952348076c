import { redirect } from 'next/navigation';

import { db } from '@/lib/database';
import { getCalendarEventModel } from '@/schemas/calendar-event/model';
import { ActionResult } from '@/lib/form';
import { CALENDAR_EVENT_TYPES } from '@/lib/calendar/constants';
import { convertFromUserTz } from '@/lib/timezone';
import { validateRequest } from '@/server/auth';

export async function create(_: unknown, formData: FormData): Promise<ActionResult> {
  'use server';

  let _id = null;

  try {
    await db();

    const { user } = await validateRequest();
    const userTz = user?.tzoffset ?? 0;

    const calendarModel = await getCalendarEventModel();

    const payload = await calendarModel.create({
      summary: formData.get('summary') + '',
      start: convertFromUserTz(new Date(formData.get('start') + ''), userTz),
      end: convertFromUserTz(new Date(formData.get('end') + ''), userTz),
      description: formData.get('description') + '',
      eventType: CALENDAR_EVENT_TYPES.APPOINTMENT,
    });

    _id = payload?._id;
  } catch (error) {
    if (error instanceof Error) return { error: error.message };

    return { error: 'Unknown error' };
  }

  if (!_id) return { error: 'Failed to create record' };

  redirect(`/calendar/${_id}`);
}
