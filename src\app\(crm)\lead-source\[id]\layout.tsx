import { Metadata, ResolvingMetadata } from 'next/types';

import { getData } from './helpers';
import { ViewLeadSourceProps } from './types';

export async function generateMetadata({ params }: ViewLeadSourceProps, parent: ResolvingMetadata): Promise<Metadata> {
  const { leadSource } = await getData(params.id);

  const name = leadSource.name || leadSource.id;
  const parentMetadata = await parent;
  const title = [name, parentMetadata.title?.absolute].filter((x) => !!x);

  return {
    title: title.join(' | '),
  };
}

export default function LeadSourceIdLayout({ children }: Readonly<{ children: React.ReactNode }>) {
  return children;
}
