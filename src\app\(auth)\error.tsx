'use client';

import Link from 'next/link';
import { useEffect } from 'react';

import { Wrapper } from '@/lib/auth';
import { logger } from '@/lib/logger';

export default function Error({ error }: { error: Error & { digest?: string; isDev?: boolean }; reset: () => void }) {
  useEffect(() => {
    logger.error(error);

    document.title = 'Error | Something went wrong!';
  }, [error]);

  return (
    <Wrapper title="Something went wrong!">
      <p className="p-1 bg-slate-800 rounded-sm font-mono projecting-none">Error: {error.message}</p>
      <p className="text-center">
        <Link className="text-orange-500 hover:underline cursor-pointer" href="/login">
          login
        </Link>
      </p>
    </Wrapper>
  );
}
