import '@/services';

import { Metadata, ResolvingMetadata } from 'next';

import { DoActionComponent, NotFound } from '@/lib/actions';
import { applyFilters } from '@/lib/filters';
import { Form, type FormAction } from '@/lib/form';
import { WidgetEdit } from '@/lib/account';
import { assert } from '@/lib/typescript';

import type { AccountServicePageProps } from './types';
import { getData } from './helpers';

export async function generateMetadata(
  { params }: AccountServicePageProps,
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const parentMetadata = await parent;

  const serviceName = await applyFilters('service_name_' + params.service, 'Service');

  return {
    title: serviceName + ' | ' + parentMetadata.title?.absolute,
  };
}

export default async function AccountServicePage({ params }: AccountServicePageProps) {
  const { service, id } = params;
  const { account } = await getData(id);

  let action: FormAction | null = null;

  try {
    const response = await applyFilters('account_service_action_' + service, action, { accountId: id });

    if (response) {
      assert<FormAction | null>(response, typeof response === 'function');

      action = response;
    }
  } catch {}

  if (action) {
    return (
      <Form action={action}>
        <WidgetEdit id={id} label={account.name}>
          <DoActionComponent name={'account_service_page_' + service} fallback={<NotFound />} accountId={id} />
        </WidgetEdit>
      </Form>
    );
  }

  return (
    <WidgetEdit readOnly id={id} label={account.name}>
      <DoActionComponent name={'account_service_page_' + service} fallback={<NotFound />} accountId={id} />
    </WidgetEdit>
  );
}
