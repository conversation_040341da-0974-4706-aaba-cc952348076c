import mongoose from 'mongoose';
import { redirect } from 'next/navigation';

import { db } from '@/lib';
import { ActionResult } from '@/lib/form';
import { assertString } from '@/lib/validators';
import { getProjectModel, type ProjectDoc } from '@/schemas';

export async function edit(
  props: { id: string; canEdit: boolean },
  prevState: unknown,
  formData: FormData,
): Promise<ActionResult> {
  'use server';
  const { id, canEdit } = props;
  if (!canEdit) return { error: 'Forbidden: you can not edit project insurance.' };

  try {
    await db();
    const projectModel = await getProjectModel();

    const project = await projectModel.findOne({ _id: new mongoose.mongo.ObjectId(id) });
    if (!project) throw new Error('No Project');
    if (project.isDead) throw new Error('Project is dead');

    const company = formData.get('insuranceCompany');

    if (!assertString(company) || !mongoose.isValidObjectId(company)) {
      throw new Error('Insurance company is required');
    }

    project.set('insurance.company', new mongoose.mongo.ObjectId(company));

    await setFormData('claimNumber', formData, project, 'insurance.claim.number');

    project.set('insurance.claim.filed', formData.get('claimFiled') === 'Yes');
    project.set('insurance.hasPaperwork', formData.get('hasPaperwork') === 'Yes');

    const dateofloss = formData.get('dateOfLoss');
    if (assertString(dateofloss) && dateofloss) {
      project.set('insurance.dateOfLoss', new Date(dateofloss));
    } else {
      project.set('insurance.dateOfLoss', undefined);
    }

    await setFormData('name', formData, project, 'adjuster.name');
    await setFormData('phone', formData, project, 'adjuster.phone');
    await setFormData('fax', formData, project, 'adjuster.fax');
    await setFormData('email', formData, project, 'adjuster.email');
    await setFormData('type', formData, project, 'adjuster.type');
    await setFormData('damageLocation', formData, project);

    project.set('adjuster.metWithAdjuster', formData.get('metWithAdjuster') === 'Yes');
    project.set('adjuster.approved', formData.get('approved') === 'Yes');

    await project.save({ timestamps: false });
  } catch (e) {
    if (e instanceof Error) {
      return { error: e.message };
    }

    return { error: 'Unknown error' };
  }

  redirect(`/project/${id}`);
}

async function setFormData(key: string, formData: FormData, project: ProjectDoc, projectKey?: string) {
  const value = formData.get(key);
  if (assertString(value) && value) {
    project.set(projectKey || key, value);
  } else {
    project.set(projectKey || key, undefined);
  }
}
