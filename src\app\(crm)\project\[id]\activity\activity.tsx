import Link from 'next/link';

import { formatDate } from '@/lib/formatting';
import { MoreLink } from '@/lib/nav';
import { ListItem, Widget } from '@/lib/widget';
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';

import { getData } from './helpers';

export async function Activity({ projectId }: Readonly<{ projectId: string }>) {
  const { project, canViewUsers, user: currentUser } = await getData(projectId);
  const { activityHistory } = project;

  const activities = activityHistory.slice(0, 5);
  const showMoreLink = activities.length !== activityHistory.length;

  return (
    <Widget label="Activity">
      <Table>
        <TableBody>
          {activities.map((item) => (
            <TableRow key={item.id}>
              <TableCell>
                <div className="flex gap-3 items-center">
                  <div className="grow flex flex-col gap-3 projecting-tight">
                    <div className="font-bold">{item.value}</div>

                    <div className="flex gap-3 text-xs">
                      {item?.documents?.user?.name && canViewUsers && (
                        <Link className="hover:no-underline underline" href={`/user/${item?.documents?.user?._id}`}>
                          {item?.documents?.user?.name}
                        </Link>
                      )}
                      <div>{formatDate(item.created, true, currentUser?.tzoffset)}</div>
                    </div>
                  </div>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {showMoreLink && (
        <ListItem>
          <MoreLink href={`/project/${projectId}/activity`} />
        </ListItem>
      )}
    </Widget>
  );
}
