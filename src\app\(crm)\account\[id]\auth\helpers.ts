import { notFound } from 'next/navigation';
import { cache } from 'react';

import type { AccountDoc } from '@/schemas';
import { getAccount } from '@/server/accounts';

import { edit } from './actions';
import { validatePermissions } from './permissions';

export const getData = cache(async function (id: string) {
  'use server';

  let account: AccountDoc | null;
  const props = await validatePermissions(id);

  try {
    ({ data: account } = await getAccount(id));
  } catch {
    notFound();
  }

  const editAction = edit.bind(null, id);

  return { ...props, currentAccount: props.account, account, editAction };
});
