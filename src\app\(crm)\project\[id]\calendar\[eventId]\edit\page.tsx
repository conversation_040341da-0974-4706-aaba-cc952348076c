import { Metadata, ResolvingMetadata } from 'next';
import Link from 'next/link';
import { notFound } from 'next/navigation';

import { Widget } from '@/lib/widget';
import { ActionFooter } from '@/lib/nav';
import { Action, Field, Form, FormButton, AssignedToField, CrewsField } from '@/lib/form';
import { toDatetimeLocal } from '@/lib/formatting/date';
import { CALENDAR_EVENT_TYPES, CALENDAR_EVENTS_DICTIONARY } from '@/lib/calendar/constants';
import { ORDER_DELIVERY_EVENT_DELIVERY_METHOD_OPTIONS } from '@/lib/order/constants';
import { ORDER_DELIVERY_EVENT_PROCUREMENT_METHOD_OPTIONS } from '@/lib/order/constants';
import { ORDER_DELIVERY_EVENT_TIME_OPTIONS } from '@/lib/order/constants';
import { Appointment } from '@/schemas/projects/subdocuments/appointment/types';
import { DeliveryEvent } from '@/schemas/projects/subdocuments/delivery-event/types';
import { LaborEvent } from '@/schemas/projects/subdocuments/labor-event/types';
import { convertToUserTz } from '@/lib/timezone';

import { checkIsAppointment, checkIsDelivery, checkIsLabor } from './helpers';
import { getData } from './actions';
import { EditCalendarEventProps } from './types';

export async function generateMetadata(_: unknown, parent: ResolvingMetadata): Promise<Metadata> {
  const parentMetadata = await parent;

  return {
    title: 'Edit | ' + parentMetadata.title?.absolute,
  };
}

export default async function EditCalendarEvent({ params }: Readonly<EditCalendarEventProps>) {
  const { deliveryEvent, laborEvent, appointment, editAction, userTz } = await getData(params.id, params.eventId);

  if (!deliveryEvent && !laborEvent && !appointment) return notFound();

  const title = deliveryEvent?.title || laborEvent?.title || appointment?.title;

  const isDelivery = checkIsDelivery(deliveryEvent?.type);
  const isLabor = checkIsLabor(laborEvent?.type);
  const isAppointment = checkIsAppointment(appointment?.type);

  return (
    <Form action={editAction} className="space-y-6">
      <Widget label={title}>
        <Field name="title" label="Label" required placeholder="Event Summary" value={title} />
        {isDelivery && <DeliveryFields data={deliveryEvent} tzOffset={userTz} />}
        {isLabor && <LaborFields data={laborEvent} tzOffset={userTz} />}
        {isAppointment && <AppointmentFields data={appointment} tzOffset={userTz} />}
      </Widget>
      <ActionFooter
        left={<Link href={`/project/${params.id}`}>Back to project</Link>}
        right={
          <>
            <FormButton>Save</FormButton>
            <Action />
          </>
        }
      />
    </Form>
  );
}

const DeliveryFields = ({ data, tzOffset }: { data: DeliveryEvent; tzOffset: number }) => {
  const { start, time, procurementMethod, deliveryMethod, description } = data;
  return (
    <>
      <Field
        name="type"
        label="Event Type"
        required
        value={CALENDAR_EVENTS_DICTIONARY[CALENDAR_EVENT_TYPES.DELIVERY]}
        readOnly
      />
      <Field name="start" label="Start" required type="date" value={convertToUserTz(new Date(start), tzOffset)} />
      <Field
        name="time"
        label="Delivery Time"
        required
        type="select"
        options={ORDER_DELIVERY_EVENT_TIME_OPTIONS}
        value={time}
      />
      <Field
        name="procurementMethod"
        label="Procurement Method"
        required
        type="select"
        options={ORDER_DELIVERY_EVENT_PROCUREMENT_METHOD_OPTIONS}
        value={procurementMethod}
      />
      <Field
        name="deliveryMethod"
        label="Delivery Method"
        required
        type="select"
        options={ORDER_DELIVERY_EVENT_DELIVERY_METHOD_OPTIONS}
        value={deliveryMethod}
      />
      <Field name="description" label="Location information" value={description} />
    </>
  );
};

const LaborFields = ({ data, tzOffset }: { data: LaborEvent; tzOffset: number }) => {
  const { description, event, crew } = data;

  return (
    <>
      <Field
        name="type"
        label="Event Type"
        required
        value={CALENDAR_EVENTS_DICTIONARY[CALENDAR_EVENT_TYPES.LABOR]}
        readOnly
      />
      <Field name="start" label="Start" value={convertToUserTz(new Date(event.start), tzOffset)} required type="date" />
      <Field name="end" label="End" value={convertToUserTz(new Date(event.end), tzOffset)} required type="date" />
      <CrewsField value={crew ? { value: crew.id, label: crew.name } : undefined} />
      <Field name="description" label="Crew instructions" value={description} />
    </>
  );
};

const AppointmentFields = ({ data, tzOffset }: { data: Appointment; tzOffset: number }) => {
  const { start, end, description, assignedTo } = data;

  return (
    <>
      <Field
        name="start"
        label="Start"
        required
        step={0}
        type="datetime-local"
        value={toDatetimeLocal(new Date(convertToUserTz(start, tzOffset)))}
      />
      <Field name="end" label="End" required type="datetime-local" step={0} value={toDatetimeLocal(new Date(end))} />
      <Field
        name="type"
        label="Event Type"
        required
        value={CALENDAR_EVENTS_DICTIONARY[CALENDAR_EVENT_TYPES.APPOINTMENT]}
        readOnly
      />
      <AssignedToField value={assignedTo ? { value: assignedTo._id.toString(), label: assignedTo.name } : undefined} />
      <Field name="description" label="Description" value={description} />
    </>
  );
};
