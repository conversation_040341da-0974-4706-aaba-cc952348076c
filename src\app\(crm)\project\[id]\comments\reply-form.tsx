'use client';
import { useState } from 'react';

import { Field, Form, FormButton } from '@/lib/form';
import { socket } from '@/socket';
import { Button } from '@/components/ui/button';

import { COMMENT_TYPE } from './comment-types';
// Import the server action properly
import { postCommentAction } from './client-actions';

interface Props {
  projectId: string;
  parentCommentId: string;
}

export function ReplyForm({ projectId, parentCommentId }: Props) {
  const [isReplying, setIsReplying] = useState(false);

  const handleSubmit = async (prevState: unknown, formData: FormData) => {
    // Add the parent comment ID to the form data
    formData.append('parentComment', parentCommentId);

    // Add a timestamp to ensure page refresh
    formData.append('timestamp', Date.now().toString());

    // Use the imported server action
    const res = await postCommentAction(projectId, COMMENT_TYPE.CRM, prevState, formData);

    // Notify other clients to refresh
    socket.emit('post-comment');

    // Force a page refresh to show the new reply
    if (!res.error) {
      setIsReplying(false);

      // Use a more targeted refresh approach that doesn't close all toggles
      // Get any existing toggle states from localStorage first
      try {
        const toggleStates = localStorage.getItem('commentToggleStates');

        // This gives the server time to process the comment before refresh
        window.setTimeout(() => {
          // Store toggle states temporarily
          if (toggleStates) {
            sessionStorage.setItem('commentToggleStatesBackup', toggleStates);
          }

          // Force a refresh but indicate we want to restore toggles after
          sessionStorage.setItem('preserveToggles', 'true');
          window.location.reload();
        }, 500);
      } catch (e: unknown) {
        // Fall back to regular reload if local storage fails
        // Log the error using a custom logging mechanism or handle it appropriately
        // Example: logError('Error saving toggle states:', e);
        window.setTimeout(() => {
          window.location.reload();
        }, 500);
      }
    }

    return res;
  };

  if (!isReplying) {
    return (
      <Button type="button" size="sm" variant="secondary" onClick={() => setIsReplying(true)}>
        Reply
      </Button>
    );
  }

  return (
    <>
      <div className="ml-8 pl-4 mt-2">
        <Form action={handleSubmit}>
          <div className="flex flex-col gap-2">
            <Field name="comment" type="textarea" label="Reply" mentions rows={3} />
            <input type="hidden" value={projectId} name="_id" readOnly />
            <div className="flex justify-end gap-2">
              <button
                type="button"
                onClick={() => setIsReplying(false)}
                className="px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 rounded"
              >
                Cancel
              </button>
              <FormButton>Post Reply</FormButton>
            </div>
          </div>
        </Form>
      </div>
    </>
  );
}
