import { type User, type UserDoc, type ProjectDoc } from '@/schemas';
import { getProject } from '@/server/projects';

import { deleteComment, revalidateComments } from './actions';
import { validatePermissions } from './permissions';

export function assertUserDoc(user: User | UserDoc | undefined): user is UserDoc {
  return !!user && 'id' in user;
}

export async function getData(id: string) {
  'use server';

  const { data } = await getProject(id);
  const props = await validatePermissions(data);
  const revalidateCommentsAction = revalidateComments.bind(null, id);

  return {
    ...props,
    deleteComment,
    revalidateComments: revalidateCommentsAction,
  };
}

export function getCommentReplies(
  comments: ProjectDoc['comments'] | Array<ProjectDoc['comments'][number]>,
  commentId: string,
): Array<ProjectDoc['comments'][number]> {
  return comments.filter((comment) => comment.parentComment?.toString() === commentId);
}

export function getTopLevelComments(
  comments: ProjectDoc['comments'] | Array<ProjectDoc['comments'][number]>,
): Array<ProjectDoc['comments'][number]> {
  return comments.filter((comment) => !comment.parentComment);
}
