import React from 'react';

import { ProjectDoc } from '@/schemas';

export interface WidgetProps {
  action: React.ReactNode;
  project: ProjectDoc;
}

export type PageProps = { params: { id: string } };

export interface DimensionFieldProps {
  name?: string;
  value?: string;
  unit?: string;
}

export interface EagleviewButtonProps {
  children: React.ReactNode;
}

export interface ItemDeleteButtonProps {
  name: string;
}
