import { cache } from 'react';

import { db } from '@/lib';
import { logger } from '@/lib/logger';
import { getUserModel } from '@/schemas/users';
import { ServerError } from '@/server';

export const getData = cache(async function _getData(confirmToken?: string) {
  'use server';

  const payload = { isConfirming: false };

  if (!confirmToken) return payload;

  try {
    await db();
    const userModel = await getUserModel();
    const user = await userModel.findOne({ confirmToken });

    if (user?._id) payload.isConfirming = true;
  } catch (error: unknown) {
    if (error instanceof Error) throw new ServerError(error.message);

    logger.log(error);
    throw new ServerError('Unknown error.');
  }

  if (!payload.isConfirming)
    throw new ServerError(
      'The password reset link you used is no longer valid. Please request a new password reset if necessary.',
    );

  return payload;
});
