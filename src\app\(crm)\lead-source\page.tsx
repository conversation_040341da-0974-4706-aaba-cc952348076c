import Link from 'next/link';

import { formatDate } from '@/lib/formatting';
import { ListScreen } from '@/lib/screen';
import { SCHEMAS } from '@/lib/schemas';
import { Cell } from '@/lib/table';
import type { LeadSourceDoc } from '@/schemas';
import { validateRequest } from '@/server/auth';

export default async function ListLeadSources({ searchParams }: { searchParams: { page?: string } }) {
  const { user } = await validateRequest();

  return (
    <ListScreen<LeadSourceDoc>
      {...{ searchParams }}
      model={SCHEMAS.LEAD_SOURCE}
      label="Lead Sources"
      heading={
        <>
          <Cell>Name</Cell>
          <Cell>Created</Cell>
        </>
      }
      body={async (item) => {
        return (
          <>
            <Cell scope="row">
              <Link href={`/lead-source/${item.id}`} className="hover:underline">
                {item.name}
              </Link>
            </Cell>
            <Cell>{formatDate(item.created, true, user?.tzoffset)}</Cell>
          </>
        );
      }}
    />
  );
}
