import Link from 'next/link';
import { Metadata, ResolvingMetadata } from 'next';

import { Field, Form, FormButton } from '@/lib/form';
import { ActionFooter } from '@/lib/nav';
import { Widget } from '@/lib/widget';

import { create } from './actions';
import { DomainField } from './domain-field';
import { validatePermissions } from './permissions';

export async function generateMetadata(_: unknown, parent: ResolvingMetadata): Promise<Metadata> {
  const parentMetadata = await parent;

  return {
    title: 'Create | ' + parentMetadata.title?.absolute,
  };
}

export default async function CreateAccount() {
  await validatePermissions();

  return (
    <Form action={create} className="space-y-6">
      <Widget label="New Account">
        <div className="grid grid-cols-1 gap-4">
          <Field name="name" label="Name" required />
          <Field name="prefix" label="Prefix" required />
          <DomainField />
        </div>
      </Widget>

      <ActionFooter left={<Link href="/account">Back to Accounts</Link>} right={<FormButton>Save</FormButton>} />
    </Form>
  );
}
