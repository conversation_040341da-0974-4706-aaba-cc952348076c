import { cache } from 'react';

import { getInsurance } from '@/server/insurance';

import { validatePermissions } from './permissions';

export const getData = cache(async function (id: string) {
  'use server';
  const { data } = await getInsurance(id);
  const { canEdit, canView, user, account, insurance } = await validatePermissions(data);

  return { canEdit, canView, user, account, insurance };
});
