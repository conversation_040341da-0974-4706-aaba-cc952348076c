'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import dynamic from 'next/dynamic';
import { MbscCalendarEvent, MbscEventCreateEvent } from '@mobiscroll/react';

import '@mobiscroll/react/dist/css/mobiscroll.min.css';
import '@/lib/calendar/project-calendar.css';

import { Widget } from '@/lib/widget';
import { Field, Button } from '@/lib/form';
import { CALENDAR_EVENT_TYPES } from '@/lib/calendar/constants';
import { CalendarEvent } from '@/lib/calendar/calendar-event';
import { CalendarDay } from '@/lib/calendar/calendar-day';

const Eventcalendar = dynamic(() => import('@mobiscroll/react').then((mod) => mod.Eventcalendar), {
  ssr: false,
  loading: () => <div className="flex items-center justify-center p-4">Loading calendar...</div>,
});

interface UserCalendarProps {
  events: MbscCalendarEvent[];
  projectId: string;
}

type CalendarView = 'week' | 'month' | 'year';
const viewTypes = [
  { value: 'week', label: 'Week' },
  { value: 'month', label: 'Month' },
  { value: 'year', label: 'Year' },
];

export function CalendarAgenda({ events, projectId }: UserCalendarProps) {
  const router = useRouter();
  const [calendarView, setCalendarView] = useState<CalendarView>('month');

  const onCreate = (selectedDate = '') => {
    router.push(
      `/project/${projectId}/calendar/create?type=${CALENDAR_EVENT_TYPES.APPOINTMENT}&date=${selectedDate ?? ''}`,
    );
  };

  const onEventClick = ({ eventId }: { eventId?: string }) => {
    if (!eventId) return;

    router.push(`/project/${projectId}/calendar/${eventId}`);
  };

  const handleCalendarViewChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    // eslint-disable-next-line no-type-assertion/no-type-assertion
    setCalendarView(e.target.value as CalendarView);
  };

  const handleSelectEvent = (data?: MbscEventCreateEvent) => {
    onCreate(data?.event.start?.toString());
  };

  return (
    <Widget label="Calendar View" action={<Button onClick={() => onCreate()}>Create Event</Button>}>
      <Field
        name="calendarView"
        label="Calendar View"
        type="select"
        value={calendarView}
        options={viewTypes}
        onChange={handleCalendarViewChange}
      />
      <Eventcalendar
        theme="windows-dark"
        onEventCreate={handleSelectEvent}
        onEventClick={({ event }) => onEventClick({ eventId: event.id?.toString() })}
        clickToCreate
        renderEvent={({ original }) => <CalendarEvent {...original} onClick={onEventClick} />}
        renderLabelContent={({ original }) => <CalendarDay {...original} />}
        data={events}
        view={{
          calendar: { type: calendarView, labels: 'all' },
          agenda: { type: 'day', scrollable: true },
        }}
      />
    </Widget>
  );
}
