import { notFound } from 'next/navigation';
import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { ProjectDoc } from '@/schemas';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function (project: ProjectDoc | null, invoiceId: string) {
  'use server';

  const { user, account, isSystemAdmin } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not edit project invoices.');

  const sameAccount = account._id.equals(project?.account) || isSystemAdmin;

  const canEdit = await can(user, 'edit_project_invoices', sameAccount);
  if (!canEdit) throw new ServerError('Forbidden: you can not edit project invoices.');
  const canDelete = await can(user, 'delete_project_invoices');

  if (!project?._id) notFound();

  const invoice = project.invoices.find((o) => o.id === invoiceId);
  if (!invoice) notFound();

  if (invoice.readOnly || project.readOnly) throw new ServerError('Forbidden: you can not edit readonly invoices.');

  return { user, account, project, invoice, canEdit: canEdit && !project.readOnly && !invoice.readOnly, canDelete };
});
