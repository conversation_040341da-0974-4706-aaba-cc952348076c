import { notFound } from 'next/navigation';
import { cache } from 'react';

import type { AccountDoc } from '@/schemas';
import { getAccount } from '@/server/accounts';

import { validatePermissions } from './permissions';

export const getData = cache(async function (id: string) {
  'use server';

  let account: AccountDoc | null;
  const props = await validatePermissions();

  try {
    ({ data: account } = await getAccount(id));
  } catch {
    notFound();
  }

  return { ...props, currentAccount: props.account, account };
});
