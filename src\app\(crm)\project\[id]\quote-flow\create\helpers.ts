import { cache } from 'react';

import { getProject } from '@/server/projects';

import { create } from './actions';
import { validatePermissions } from './permissions';

export const getData = cache(async function (projectId: string) {
  'use server';
  const { data } = await getProject(projectId);
  const props = await validatePermissions(data);
  const createAction = create.bind(null, projectId);

  return { ...props, createAction };
});
