import Link from 'next/link';
import type { Metadata, ResolvingMetadata } from 'next';

import { Field, Form, FormButton } from '@/lib/form';
import { ActionFooter } from '@/lib/nav';
import { Widget } from '@/lib/widget';

import { create } from './actions';
import { validatePermissions } from './permissions';

export async function generateMetadata(_: unknown, parent: ResolvingMetadata): Promise<Metadata> {
  const parentMetadata = await parent;

  return {
    title: 'Create | ' + parentMetadata.title?.absolute,
  };
}

export default async function CreatePage() {
  const { account } = await validatePermissions();

  return (
    <Form action={create} className="space-y-4">
      <Widget label={'New Lead Source'}>
        <div className="grid grid-cols-1 gap-4">
          <Field name="name" label="Name" required />
        </div>
      </Widget>
      <ActionFooter
        left={<Link href={`/account/${account._id.toString()}/custom-fields`}>Back to Lead Sources</Link>}
        right={<FormButton>Save</FormButton>}
      />
    </Form>
  );
}
