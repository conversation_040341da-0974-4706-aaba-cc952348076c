'use server';

import { redirect } from 'next/navigation';

import { db } from '@/lib';
import { ActionResult } from '@/lib/form';
import { getInsuranceCompanyModel } from '@/schemas';

import { validatePermissions } from './permissions';

export async function create(_: unknown, formData: FormData): Promise<ActionResult> {
  let _id = null;

  try {
    await db();
    await validatePermissions();

    const InsuranceModel = await getInsuranceCompanyModel();
    const insurance = new InsuranceModel({
      name: formData.get('name'),
    });

    const response = await insurance.save();

    _id = response?._id;
  } catch (error) {
    if (error instanceof Error) return { error: error.message };

    return { error: 'Unknown error' };
  }

  if (!_id) return { error: 'Failed to create record' };

  redirect(`/insurance/${_id}`);
}
