import { Metadata, ResolvingMetadata } from 'next/types';

import { getData } from './helpers';

type Props = {
  params: { id: string };
};

export async function generateMetadata({ params }: Props, parent: ResolvingMetadata): Promise<Metadata> {
  const { customer } = await getData(params.id);

  const name = customer.name || customer.id;
  const parentMetadata = await parent;
  const title = [name, parentMetadata.title?.absolute].filter((x) => !!x);

  return {
    title: title.join(' | '),
  };
}

export default function CustomerIdLayout({ children }: Readonly<{ children: React.ReactNode }>) {
  return children;
}
