import { cache } from 'react';
import { notFound } from 'next/navigation';

import { getProject } from '@/server/projects';
import QuickBooksClient from '@/server/quickbooks-client/client';
import { getAccountModel, getProjectInvoiceIntegrationModel } from '@/schemas';

import { validatePermissions } from './permissions';

export const getData = cache(async function (id: string, invoiceId: string) {
  'use server';
  const { data } = await getProject(id);
  const invoice = data?.invoices?.find((invoice) => invoice._id.equals(invoiceId));

  const props = await validatePermissions(data, invoice);

  const AccountModel = await getAccountModel();

  const account = await AccountModel.findOne({ _id: data?.account._id.toString() });

  if (!account?.settings?.quickBooks) {
    return notFound();
  }

  const qb = new QuickBooksClient(
    account._id.toString(),
    account.settings.quickBooks.clientId,
    account.settings.quickBooks.clientSecret,
    account.settings.quickBooks.credentials.access_token,
    account.settings.quickBooks.credentials.refresh_token,
    account.settings.quickBooks.redirectUri,
    account.settings.quickBooks.credentials.realm_id,
    account.settings.quickBooks.environment,
  );

  const ProjectInvoiceIntegrationModel = await getProjectInvoiceIntegrationModel();
  const projectInvoiceIntegration = await ProjectInvoiceIntegrationModel.findOne({ invoice: invoiceId });

  if (!projectInvoiceIntegration?.quickBooksInvoiceId) {
    return notFound();
  }

  const quickBooksInvoicePdf = await qb.getInvoicePdf(projectInvoiceIntegration.quickBooksInvoiceId);
  const arrayBuffer = await quickBooksInvoicePdf.arrayBuffer();
  const buffer = Buffer.from(arrayBuffer);
  // Convert to a Base64 URL
  const base64Pdf = buffer.toString('base64');
  const quickBooksInvoicePdfUrl = `data:application/pdf;base64,${base64Pdf}`;

  return { ...props, quickBooksInvoicePdfUrl };
});
