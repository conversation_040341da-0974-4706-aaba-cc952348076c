name: Lint

on:
  push:

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

env:
  NODE_VERSION: lts/iron

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
      - id: cache-deps
        uses: actions/cache@v4
        env:
          cache-name: cache-node-modules
        with:
          path: |
            node_modules/
          key: ${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
      - if: ${{ steps.cache-deps.outputs.cache-hit != 'true' }}
        run: npm i
      - run: npm run lint
