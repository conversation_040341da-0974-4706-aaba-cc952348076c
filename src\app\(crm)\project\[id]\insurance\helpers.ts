import { cache } from 'react';

import { getProject } from '@/server/projects';

import { edit } from './actions';
import { validatePermissions } from './permissions';

export const getData = cache(async function (id: string) {
  'use server';
  const { data } = await getProject(id);
  const props = await validatePermissions(data);
  const editAction = edit.bind(null, { id, canEdit: props.canEdit });

  return { ...props, editAction };
});
