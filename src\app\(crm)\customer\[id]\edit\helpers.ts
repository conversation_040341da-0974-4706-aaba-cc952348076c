import { cache } from 'react';

import { getCustomer } from '@/server/customer';

import { edit } from './actions';
import { validatePermissions } from './permissions';

export const getData = cache(async function (id: string) {
  'use server';
  const { data } = await getCustomer(id);
  const { canEdit, canDelete, user, account, customer } = await validatePermissions(data);
  const editAction = edit.bind(null, id, canDelete);

  return { canEdit, canDelete, user, account, customer, editAction };
});
