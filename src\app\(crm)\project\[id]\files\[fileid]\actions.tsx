import mongoose from 'mongoose';
import { revalidatePath } from 'next/cache';

import { db } from '@/lib';
import { ActionResult } from '@/lib/form';
import { getProjectModel } from '@/schemas';
import { getProjectSignatureModel, SIGNATURE_TYPE } from '@/schemas/projects/subdocuments';

import { getData, getSignatureName } from './helpers';

export async function sign(type: SIGNATURE_TYPE, projectId: string, fileId: string): Promise<ActionResult> {
  'use server';
  const { canSignAsAdmin, canSignWithCustomerInperson, canSignAsPm } = await getData(projectId, fileId);

  if (type === SIGNATURE_TYPE.CUSTOMER && !canSignWithCustomerInperson) {
    return { error: 'Forbidden: You do not have permission to sign with inperson with the customer.' };
  } else if (type === SIGNATURE_TYPE.ADMIN && !canSignAsAdmin) {
    return { error: 'Forbidden: You do not have permission to sign as an admin.' };
  } else if (type === SIGNATURE_TYPE.PM && !canSignAsPm) {
    return { error: 'Forbidden: You do not have permission to sign as a project manager.' };
  }

  try {
    await db();
    const projectModel = await getProjectModel();
    const project = await projectModel
      .findOne({ _id: new mongoose.mongo.ObjectId(projectId) })
      .populate(['documents.assignedTo', { path: 'files', populate: 'documents.file' }]);
    if (!project) throw new Error('Not found: project is not found');

    const file = project.files.find((f) => f.file.toString() === fileId);
    if (!file || !file.documents?.file) throw new Error('Not found: file is not found');

    const existingSignature = file.signatures.find((s) => s.type === type);
    if (existingSignature) throw new Error('Error: this signature was already captured');

    const signatureModel = await getProjectSignatureModel();
    const name = await getSignatureName(type, projectId, fileId);
    const signature = new signatureModel({ type, name, hash: 'demo', ipAddress: 'demo' });

    file.signatures.push(signature);
    await project.save({ timestamps: false });
  } catch (error: unknown) {
    if (error instanceof Error) return { error: error.message };

    return { error: 'Unknown error' };
  }

  revalidatePath(`/project/${projectId}/files/${fileId}`);
  return { error: null };
}
