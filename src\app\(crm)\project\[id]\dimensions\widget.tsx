import { DisplayData } from '@/lib/view';
import { NotFound, Widget } from '@/lib/widget';

import { WidgetProps } from './types';

export function DimensionsWidget(props: Readonly<WidgetProps>) {
  const { dimensions } = props.project;
  const hasDimensions = !!dimensions && dimensions.length > 0;

  return (
    <Widget label="Dimensions" action={props.action}>
      {hasDimensions ? (
        <div className="grid grid-cols-1 gap-4">
          {dimensions.map((dimension) => (
            <DisplayData
              key={dimension.name}
              label={dimension.name}
              value={
                Number(dimension.value).toLocaleString('en-US', { maximumFractionDigits: 2 }) + ' ' + dimension.unit
              }
            />
          ))}
        </div>
      ) : (
        <NotFound>No dimensions available</NotFound>
      )}
    </Widget>
  );
}
