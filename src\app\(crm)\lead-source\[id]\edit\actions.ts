'use server';

import mongoose from 'mongoose';
import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

import { db } from '@/lib';
import { ActionResult } from '@/lib/form';
import { getLeadSourceModel } from '@/schemas';

export async function edit(id: string, canDelete: boolean, _: unknown, formData: FormData): Promise<ActionResult> {
  'use server';
  const { _action } = Object.fromEntries(formData);

  if (_action === 'delete') {
    if (!canDelete) return { error: 'Forbidden: you do not have permissions to delete this lead source.' };

    try {
      await db();
      const leadSourceModel = await getLeadSourceModel();
      await leadSourceModel.deleteOne({ _id: new mongoose.mongo.ObjectId(id) });
    } catch (e) {
      if (e instanceof Error) return { error: e.message };

      return { error: 'Unknown error' };
    }

    redirect(`/lead-source`);
  }

  try {
    await db();
    const leadSourceModel = await getLeadSourceModel();

    const { name } = Object.fromEntries(formData);
    const leadSource = await leadSourceModel.findById(id);
    if (!leadSource) return { error: 'Lead Source not found' };

    leadSource.set('name', name);

    await leadSource.save();
  } catch (e) {
    if (e instanceof Error) return { error: e.message };

    return { error: 'Unknown error' };
  }

  revalidatePath(`/lead-source/${id}`);
  redirect(`/lead-source`);
}
