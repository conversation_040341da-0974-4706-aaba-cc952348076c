'use client';

import { useEffect } from 'react';

import { ActionResult, Field, Form, FormButton } from '@/lib/form';
import { socket } from '@/socket';

import { COMMENT_TYPE } from './comment-types';
import { postCommentAction } from './client-actions';

interface Props {
  projectId: string;
  commentType: COMMENT_TYPE;
  revalidateComments: (id: string) => Promise<ActionResult>;
}

export function CreateCommentForm({ projectId, commentType, revalidateComments }: Props) {
  useEffect(() => {
    socket.on('refetch-comments', async () => {
      await revalidateComments(projectId);
    });
  }, [projectId, revalidateComments]);

  return (
    <Form
      action={async (_, formData) => {
        const res = await postCommentAction(projectId, commentType, _, formData);
        socket.emit('post-comment');
        return res;
      }}
    >
      <div className="flex flex-col gap-4">
        <Field name="comment" type="textarea" label="Post a comment" mentions />
        <input type="hidden" value={projectId} name="_id" readOnly />
        <div className="flex justify-end">
          <FormButton>Post</FormButton>
        </div>
      </div>
    </Form>
  );
}
