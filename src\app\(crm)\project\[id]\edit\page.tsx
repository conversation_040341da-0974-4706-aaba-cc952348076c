import Link from 'next/link';
import { Metadata, ResolvingMetadata } from 'next/types';

import { CustomerPicker } from '@/lib/customers';
import { Action, AddressFields, Field, Form, FormButton, AssignedToField } from '@/lib/form';
import { convertDbRecordToSelectOption } from '@/lib/form/convert-db-record-to-select-option';
import { ActionFooter } from '@/lib/nav';
import { EditIcon, Widget } from '@/lib/widget';
import { PROJECTCATEGORY, PROJECTPRIORITY, PROJECTTYPES, PROJECTSUBTYPES } from '@/schemas';
import { makeClientSafe } from '@/server';
import { EventItem } from '@/lib/calendar/event-item';
import { serializeCalendarEvents } from '@/lib/calendar/serialize';
import { ListItem, NotFound, Sticky } from '@/lib/widget';
import { MoreLink } from '@/lib/nav';
import { CALENDAR_EVENT_TYPES } from '@/lib/calendar/constants';

import { LocationField } from './fields';
import { getData } from './helpers';
import { ProjectEditProps } from './types';

export async function generateMetadata(_: unknown, parent: ResolvingMetadata): Promise<Metadata> {
  const parentMetadata = await parent;

  return {
    title: 'Edit | ' + parentMetadata.title?.absolute,
  };
}

export default async function EditProjectInfo({ params }: Readonly<ProjectEditProps>) {
  const { project, editAction, canInputCoords, canDelete, leadSources } = await getData(params.id);

  const { tzoffset } = await getData(params.id).then((data) => data.user || { tzoffset: 0 });
  const eventsToShow = serializeCalendarEvents(project, tzoffset || 0).slice(0, 5);
  const projectId = params.id;

  return (
    <Form action={editAction} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Widget label="Details" rowSpan={2} className="grid grid-cols-1 gap-4">
          <AssignedToField value={await convertDbRecordToSelectOption(project.documents?.assignedTo)} />
          <Field
            value={project.jobPriority}
            required
            name="jobPriority"
            label="Job Priority"
            type="radio"
            options={Object.values(PROJECTPRIORITY)}
          />

          <Field
            value={project.jobCategory}
            required
            name="jobCategory"
            label="Job Category"
            type="radio"
            options={Object.values(PROJECTCATEGORY)}
          />

          <Field
            value={project.jobType}
            required
            name="jobType"
            label="Job Type"
            type="radio"
            options={Object.values(PROJECTTYPES)}
          />

          <Field
            value={project.jobSubType}
            name="jobSubType"
            label="Job Subtype"
            type="radio"
            options={Object.values(PROJECTSUBTYPES)}
          />

          <Field
            value={project.leadSource}
            required
            name="leadSource"
            label="Lead Source"
            type="select"
            options={leadSources.map((leadSource) => leadSource.name)}
          />

          <Field value={project.companyName} name="companyName" label="Company Name" />

          <LocationField value={await convertDbRecordToSelectOption(project.documents?.location)} />

          <AddressFields value={project.address} required name="address" label="Address" editCoord={canInputCoords} />
        </Widget>

        <div className="flex flex-col gap-6">
          <CustomerPicker
            customerAction={
              <div className="flex gap-6">
                {!!project.documents?.customer && (
                  <EditIcon title="Edit Customer" href={`/customer/${project.documents.customer._id.toString()}`} />
                )}
              </div>
            }
            customer={makeClientSafe(project.documents?.customer)}
          />

          {/* Calendar Section */}
          <Widget label="Calendar Events">
            <Sticky
              bottom={
                <ListItem>
                  <Link
                    className="text-blue-600 hover:underline"
                    href={`/project/${projectId}/calendar/create?type=${CALENDAR_EVENT_TYPES.APPOINTMENT}`}
                  >
                    + Add New Appointment
                  </Link>
                  <div className="ml-auto">
                    <MoreLink href={`/project/${projectId}/calendar`}>View All Events</MoreLink>
                  </div>
                </ListItem>
              }
            >
              {eventsToShow.length > 0 ? (
                eventsToShow.map((item) => (
                  <ListItem key={item.id}>
                    <EventItem projectId={projectId} item={item} tzoffset={tzoffset || 0} />
                  </ListItem>
                ))
              ) : (
                <NotFound>No events scheduled</NotFound>
              )}
            </Sticky>
          </Widget>
        </div>
      </div>

      <ActionFooter
        left={
          <Link href={`/project/${project._id.toString()}`} className="whitespace-nowrap">
            Back to Project
          </Link>
        }
        right={
          <>
            <FormButton>Save</FormButton>
            {canDelete && <Action />}
          </>
        }
      />
    </Form>
  );
}
