'use client';

import Link from 'next/link';
import { useState } from 'react';

import { formatPhoneNumber } from '@/lib/formatting';
import { Widget } from '@/lib/widget';
import { Table, TableBody, TableCell, TableHeader, TableRow } from '@/components/ui/table';

import { CustomerSimilarity } from './actions';

export default function SimilarCustomers({
  customers,
  title = 'Similar Customers Found',
  description = 'We found similar customer records in the system. Please review these before creating a new customer.',
  titleClass = 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-700/30',
}: {
  customers: CustomerSimilarity[];
  title?: string;
  description?: string;
  titleClass?: string;
}) {
  const [expanded, setExpanded] = useState(true);

  if (!customers || customers.length === 0) return null;

  return (
    <Widget
      label={title}
      className={titleClass}
      action={
        <button
          onClick={() => setExpanded(!expanded)}
          className="text-sm font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
        >
          {expanded ? 'Hide' : 'Show'}
        </button>
      }
    >
      {expanded && (
        <>
          <div className="flex items-center p-4 mb-4 text-yellow-800 border-l-4 border-yellow-400 bg-yellow-100 dark:bg-yellow-800/30 dark:text-yellow-300 dark:border-yellow-600">
            <svg
              className="flex-shrink-0 w-5 h-5 mr-3"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clipRule="evenodd"
              ></path>
            </svg>
            <div>
              <p className="font-medium">{title}</p>
              <p className="text-sm mt-1">{description}</p>
            </div>
          </div>

          <div className="relative overflow-x-auto border border-gray-200 dark:border-gray-700 rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Phone</TableCell>
                  <TableCell>Email</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHeader>
              <TableBody>
                {customers
                  .filter((customer) => customer && customer._id)
                  .map((customer) => (
                    <TableRow
                      key={customer._id?.toString() || 'unknown'}
                      className="bg-white border-b dark:bg-gray-800 dark:border-gray-700"
                    >
                      <TableCell className="px-6 py-4 font-medium whitespace-nowrap">
                        {customer.name || 'Unknown Name'}
                      </TableCell>
                      <TableCell className="px-6 py-4">
                        {(customer.phoneNumbers || []).map((phone) => formatPhoneNumber(phone)).join(', ')}
                      </TableCell>
                      <TableCell className="px-6 py-4">{(customer.emailAddresses || []).join(', ')}</TableCell>
                      <TableCell className="px-6 py-4">
                        <div className="flex space-x-3">
                          <Link
                            href={`/customer/${customer._id}`}
                            className="text-sm font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                          >
                            View
                          </Link>
                          <Link
                            href={`/customer/${customer._id}/edit`}
                            className="text-sm font-medium text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300"
                          >
                            Edit
                          </Link>
                          <Link
                            href={`/project/create?customer=${customer._id?.toString()}`}
                            className="text-sm font-medium text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-300"
                          >
                            Create Project
                          </Link>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </div>

          <div className="mt-4 p-4 bg-purple-100 dark:bg-purple-900/40 rounded-md border-l-4 border-purple-500 dark:border-purple-600">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg
                  className="h-5 w-5 text-purple-600 dark:text-purple-300"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-bold text-purple-800 dark:text-purple-200">PRO TIP</h3>
                <p className="text-sm text-purple-700 dark:text-purple-300 mt-1">
                  If you find a matching customer record, you can click <span className="font-bold">Edit</span> to
                  update their information or <span className="font-bold">Create Project</span> to start a new project
                  with this customer instead of creating a duplicate.
                </p>
              </div>
            </div>
          </div>
        </>
      )}
    </Widget>
  );
}
