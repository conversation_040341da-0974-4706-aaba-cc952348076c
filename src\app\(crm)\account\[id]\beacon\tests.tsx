import { validateRequest } from '@/server/auth';
import { DisplayData } from '@/lib/view';
import { ListItem } from '@/lib/widget';
import { BeaconApi, IBeaconAccount } from '@/services';

export async function testAction(): Promise<{ error?: string; success?: boolean; beacon?: BeaconApi }> {
  const { account } = await validateRequest();

  const beacon = account ? new BeaconApi(account._id) : null;

  if (beacon) {
    const result = await beacon.testConnection();

    return { ...result, beacon };
  }

  return {};
}

export async function TestConnection() {
  const test = await testAction();

  if (test.success && test.beacon)
    return (
      <>
        <DisplayData label="Connection Status" value="Online" />
        <AdditionalTests beacon={test.beacon} />
      </>
    );
  if (test.error) return <DisplayData label="Connection Error" value={test.error} />;

  return null;
}

async function AdditionalTests(props: Readonly<{ beacon: BeaconApi }>) {
  const accountResults = await props.beacon.getAccounts();

  if (accountResults.accounts?.length) {
    return accountResults.accounts.map((account) => {
      return (
        <ListItem key={account.accountLegacyId}>
          <TestAccount {...{ account }} />
          <TestItems accountId={account.accountLegacyId} beacon={props.beacon} />
        </ListItem>
      );
    });
  }

  return null;
}

async function TestAccount(props: Readonly<{ account: IBeaconAccount }>) {
  return (
    <>
      <DisplayData label="Account" value={props.account.accountName} />
    </>
  );
}

async function TestItems(props: Readonly<{ accountId: string; beacon: BeaconApi }>) {
  const itemResponse = await props.beacon.getItemsPage({ accountId: props.accountId });

  return <DisplayData label="Item Count" value={itemResponse?.totalNumRecs?.toString()} />;
}
