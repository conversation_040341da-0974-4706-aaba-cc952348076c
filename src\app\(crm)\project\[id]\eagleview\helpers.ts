import { notFound } from 'next/navigation';
import { cache } from 'react';

import { getProject } from '@/server/projects';
import { validateRequest } from '@/server/auth';
import { EagleviewReportResponse } from '@/services/eagleview/types';
import { UNIT } from '@/schemas';
import { ProjectDimensions } from '@/schemas/projects/subdocuments';
import { EagleviewApi, EAGLEVIEW_FILE_FORMAT, EAGLEVIEW_FILE_TYPE } from '@/services/eagleview';

import { edit, copyToDimensions } from './actions';

export const getData = cache(async function _getEagleviewReport(projectId: string) {
  'use server';
  const { user } = await validateRequest();

  const { data: project } = await getProject(projectId);
  const reportId = project?.eagleview?.reportId;

  if (!project || !reportId) notFound();

  const eagleviewApi = new EagleviewApi(project.account);
  const report = await eagleviewApi.getReport(Number(reportId));
  const image = await eagleviewApi.getFileReport(Number(reportId), {
    fileFormat: EAGLEVIEW_FILE_FORMAT.JPG,
    fileType: EAGLEVIEW_FILE_TYPE.TOP_IMAGE,
  });
  const editAction = edit.bind(null, projectId);
  const copyToDimensionsAction = copyToDimensions.bind(null, projectId);
  const base64 = `data:image/jpeg;base64,${Buffer.from(image).toString('base64')}`;

  return {
    user,
    editAction,
    copyToDimensionsAction,
    projectDimensions: project.dimensions,
    report,
    image: base64,
  };
});

export const serializeMeasurements = (
  measurements: EagleviewReportResponse['TotalMeasurements'],
  projectDimensions: ProjectDimensions[] = [],
) => {
  const result = [];

  if (measurements.LengthRidge) {
    result.push({
      name: 'Ridge Length',
      value: Number(measurements.LengthRidge.split(' ')[0]),
      unit: UNIT.FT,
      update: false,
    });
  }

  if (measurements.LengthValley) {
    result.push({
      name: 'Valley Length',
      value: Number(measurements.LengthValley.split(' ')[0]),
      unit: UNIT.FT,
      update: false,
    });
  }

  if (measurements.LengthEave) {
    result.push({
      name: 'Eave Length',
      value: Number(measurements.LengthEave.split(' ')[0]),
      unit: UNIT.FT,
      update: false,
    });
  }

  if (measurements.LengthRake) {
    result.push({
      name: 'Rake Length',
      value: Number(measurements.LengthRake.split(' ')[0]),
      unit: UNIT.FT,
      update: false,
    });
  }

  if (measurements.LengthHip) {
    result.push({
      name: 'Hip Length',
      value: Number(measurements.LengthHip.split(' ')[0]),
      unit: UNIT.FT,
      update: false,
    });
  }

  if (measurements.LengthFlashing) {
    result.push({
      name: 'Flashing Length',
      value: Number(measurements.LengthFlashing.split(' ')[0]),
      unit: UNIT.FT,
      update: false,
    });
  }

  if (measurements.LengthStepFlashing) {
    result.push({
      name: 'Step Flashing Length',
      value: Number(measurements.LengthStepFlashing.split(' ')[0]),
      unit: UNIT.FT,
      update: false,
    });
  }

  if (measurements.LengthParapets) {
    result.push({
      name: 'Parapets Length',
      value: Number(measurements.LengthParapets.split(' ')[0]),
      unit: UNIT.FT,
      update: false,
    });
  }

  if (measurements.AreaValue) {
    result.push({ name: 'Area', value: Number(measurements.AreaValue), unit: UNIT.SQ, update: false });
  }

  if (measurements.PitchTable?.length) {
    for (const pitch of measurements.PitchTable) {
      const pitchNumber = pitch.RoofArea.split(' ')[0];
      if (!pitchNumber) continue;

      result.push({ name: `Pitch ${pitch.Pitch}`, value: Number(pitchNumber), unit: UNIT.SQ, update: false });
    }
  }

  result.forEach((dimension) => {
    if (projectDimensions.find((d) => d.name === dimension.name)) {
      dimension.update = true;
    }
  });

  return result;
};
