import { notFound } from 'next/navigation';
import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { ProjectDoc } from '@/schemas';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function (project: ProjectDoc | null) {
  'use server';
  const { user, account, isSystemAdmin } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not list files on projects.');

  const sameAccount = account._id.equals(project?.account) || isSystemAdmin;

  const canList = await can(user, 'list_project_files', sameAccount);
  if (!canList) throw new ServerError('Forbidden: you can not list files on projects.');

  const [canView, canCreateFiles, canDeleteFiles, canEditFiles, canViewUsers, canViewLocations] = await Promise.all([
    can(user, 'view_project_files', sameAccount),
    can(user, 'create_File', !project?.readOnly),
    can(user, 'delete_File', 'edit_Project', !project?.readOnly),
    can(user, 'edit_File', 'edit_Project', !project?.readOnly),
    can(user, 'view_User'),
    can(user, 'view_Location'),
  ]);

  if (!project?._id) notFound();

  return {
    canList,
    canView,
    canViewUsers,
    canDeleteFiles,
    canEditFiles,
    canViewLocations,
    user,
    canCreateFiles,
    account,
    project,
  };
});
