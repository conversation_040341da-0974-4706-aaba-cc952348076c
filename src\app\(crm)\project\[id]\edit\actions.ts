import mongoose from 'mongoose';
import { redirect } from 'next/navigation';

import { db } from '@/lib';
import { ActionResult } from '@/lib/form';
import { assertString } from '@/lib/validators';
import { getProjectModel, PROJECTCATEGORY, PROJECTPRIORITY, PROJECTTYPES, PROJECTSUBTYPES } from '@/schemas';
import { validateEnum } from '@/server';
import { formatAddressForSave } from '@/server/addresses';
import { validateCustomer } from '@/lib/project/helpers';

import { getData } from './helpers';

export async function edit(id: string, _: unknown, formData: FormData): Promise<ActionResult> {
  'use server';
  const _action = formData.get('_action');
  let redirectTo = '/project/';

  try {
    const { canDelete, canInputCoords, user } = await getData(id);

    await db();

    if (_action === 'delete') {
      if (!canDelete) return { error: 'Forbidden: you do not have permissions to delete this project.' };

      const projectModel = await getProjectModel();
      await projectModel.deleteOne({ _id: new mongoose.mongo.ObjectId(id) });
    } else {
      const projectModel = await getProjectModel();
      const project = await projectModel.findOne({ _id: new mongoose.mongo.ObjectId(id) });

      if (!project) throw new Error('No Project');
      if (project.isDead) throw new Error('Project is dead');

      const { firstName, lastName, email, phone, existingCustomer } = Object.fromEntries(formData);

      validateCustomer({ firstName, lastName, email, phone, customerId: existingCustomer });

      if (assertString(existingCustomer) && mongoose.isValidObjectId(existingCustomer)) {
        project.set('customer', new mongoose.mongo.ObjectId(existingCustomer));
      } else {
        project.set('customer', { firstName, lastName, emailAddresses: [email], phoneNumbers: [phone] });
      }

      const jobPriority = formData.get('jobPriority') + '';

      if (validateEnum(jobPriority, PROJECTPRIORITY)) {
        project.jobPriority = jobPriority;
      }

      const jobCategory = formData.get('jobCategory') + '';

      if (validateEnum(jobCategory, PROJECTCATEGORY)) {
        project.jobCategory = jobCategory;
      }

      project.leadSource = formData.get('leadSource') + '';
      project.companyName = formData.get('companyName') + '';

      const jobType = formData.get('jobType') + '';
      if (validateEnum(jobType, PROJECTTYPES)) {
        project.jobType = jobType;
      }

      // Handle job subtype - allow it to be unselected
      const jobSubType = formData.get('jobSubType')?.toString();
      if (jobSubType && jobSubType.trim() !== '' && validateEnum(jobSubType, PROJECTSUBTYPES)) {
        project.jobSubType = jobSubType;
      } else {
        // Set to undefined to unselect
        project.set('jobSubType', undefined);
      }

      const assignedTo = formData.get('assignedTo')?.toString();

      if (assignedTo) {
        project.assignedTo = new mongoose.mongo.ObjectId(assignedTo);
      } else {
        throw new Error('Assignment is required. A lead must be assigned to prevent it from being overlooked.');
      }

      const location = formData.get('location')?.toString();

      if (location) {
        project.location = new mongoose.mongo.ObjectId(location);
      } else {
        project.set('location', undefined);
      }

      project.address = {
        ...project.address,
        ...formatAddressForSave({ formData, setCoordinates: canInputCoords }),
      };
      project.modified = new Date();
      project.modifiedBy = user?._id;

      project.markModified('account');
      await project.save();

      redirectTo = `/project/${id}`;
    }
  } catch (e) {
    if (e instanceof Error) {
      return { error: e.message };
    }

    return { error: 'Unknown error' };
  }

  redirect(redirectTo);
}
