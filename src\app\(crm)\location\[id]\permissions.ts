import { notFound } from 'next/navigation';
import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { LocationDoc } from '@/schemas';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function (location: LocationDoc | null) {
  'use server';
  const { user, account, isSystemAdmin } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not edit location.');

  const sameAccount = account._id.equals(location?.account) || isSystemAdmin;

  const canView = await can(user, 'view_location', sameAccount);
  if (!canView) throw new ServerError('Forbidden: you can not edit location.');

  const canEdit = await can(user, 'edit_location');

  if (!location?._id) notFound();

  return { canView, canEdit, user, account, location };
});
