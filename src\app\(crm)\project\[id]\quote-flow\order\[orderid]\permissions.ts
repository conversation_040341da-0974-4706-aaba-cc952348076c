import { notFound } from 'next/navigation';
import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { ProjectDoc } from '@/schemas';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function (project: ProjectDoc | null) {
  'use server';
  const { user, account, isSystemAdmin } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not view this order.');

  const sameAccount = account._id.equals(project?.account) || isSystemAdmin;

  const canView = await can(user, 'view_project_orders', sameAccount);
  if (!canView) throw new ServerError('Forbidden: you can not view this order.');

  const [
    viewUsers,
    canViewSignatures,
    canEdit,
    canInvoice,
    canScheduleDeliveryEvents,
    canScheduleLaborEvents,
    canSignAsAdmin,
    canSignAsPm,
    canSignWithCustomerInperson,
  ] = await Promise.all([
    can(user, 'view_User'),
    can(user, 'view_project_signatures', sameAccount),
    can(user, 'edit_project_orders', sameAccount, !project?.readOnly),
    can(user, 'create_project_invoice', sameAccount, !project?.readOnly),
    can(user, 'schedule_delivery_events'),
    can(user, 'schedule_labor_events'),
    can(user, 'sign_as_admin', sameAccount),
    can(user, 'sign_as_pm', sameAccount),
    can(user, 'sign_with_customer_inperson', sameAccount),
  ]);

  if (!project?._id) notFound();

  return {
    user,
    account,
    project,
    canView,
    viewUsers,
    canEdit,
    canInvoice,
    canScheduleDeliveryEvents,
    canScheduleLaborEvents,
    canSignAsAdmin,
    canSignAsPm,
    canSignWithCustomerInperson,
    canViewSignatures,
  };
});
