import type { ActionResult } from '@/lib/form';
import type { Account, DeadLeadReasonDoc, Project, User } from '@/schemas';

export type ReasonEditProps = { children?: string | undefined | null; id: string };

export type KillProps = {
  title: string;
  className: string;
  action: (prevState: unknown, formData: FormData) => Promise<ActionResult>;
  children: React.ReactNode;
  deadLeadReasons: DeadLeadReasonDoc[] | { name: string; _id?: string }[];
};

export type ManualProps = { action: (prevState: unknown, formData: FormData) => Promise<ActionResult> };

export type MilestonesProps = {
  id: string;
};

export type GetDataReturn = {
  project: Project;
  user: User;
  account: Account;
  canEdit: boolean;
  deadLeadReasons: { name: string; _id?: string }[];
  advanceMilestoneAction: (prevState: unknown, formData: FormData) => Promise<ActionResult>;
  recedeMilestoneAction: (prevState: unknown, formData: FormData) => Promise<ActionResult>;
  deadReasonAction: (prevState: unknown, formData: FormData) => Promise<ActionResult>;
  makeDeadAction: (prevState: unknown, formData: FormData) => Promise<ActionResult>;
  makeAliveAction: (prevState: unknown, formData: FormData) => Promise<ActionResult>;
};

export type MilestoneIconProps = {
  symbol: React.ReactNode;
  label: string;
  milestone?: Project['milestones'][number];
  isDead: boolean;
  isCancelled?: boolean;
};

export interface IContentsProps {
  children: React.ReactNode;
  before?: boolean;
  title: string;
}
