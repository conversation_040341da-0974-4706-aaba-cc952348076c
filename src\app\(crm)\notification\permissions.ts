import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function () {
  'use server';
  const { user, account } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not list notifications.');

  const canList = await can(user, 'list_notification');
  if (!canList) throw new ServerError('Forbidden: you can not list notifications.');

  return { canList, user, account };
});
