import type { Metadata, ResolvingMetadata } from 'next';

import { getData } from './helpers';
import { LocationViewProps } from './types';

export async function generateMetadata({ params }: LocationViewProps, parent: ResolvingMetadata): Promise<Metadata> {
  const { location } = await getData(params.id);

  const name = location.name;

  const parentMetadata = await parent;
  const title = [name, parentMetadata.title?.absolute].filter((x) => !!x);

  return {
    title: title.join(' | '),
  };
}

export default function CrmLayout({ children }: Readonly<{ children: React.ReactNode }>) {
  return children;
}
