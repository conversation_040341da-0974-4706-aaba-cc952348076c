import { cache } from 'react';
import mongoose from 'mongoose';

import { db } from '@/lib';
import { getCustomerModel } from '@/schemas';
import { getLeadSources } from '@/server/lead-source';

import { validatePermissions } from './permissions';

export const getData = cache(async function (customerId?: string) {
  'use server';

  const props = await validatePermissions();
  const { data: leadSources } = await getLeadSources(props.account._id.toString());

  let customer = null;
  if (customerId && mongoose.isValidObjectId(customerId)) {
    await db();
    const customerModel = await getCustomerModel();
    customer = await customerModel.findOne({ _id: new mongoose.Types.ObjectId(customerId) });
  }

  return { ...props, leadSources, customer };
});
