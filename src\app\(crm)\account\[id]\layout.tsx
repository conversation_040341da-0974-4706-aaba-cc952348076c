import { Metadata, ResolvingMetadata } from 'next/types';

import { getAccount } from '@/server/accounts';

type Props = {
  params: { id: string };
};

export async function generateMetadata({ params }: Props, parent: ResolvingMetadata): Promise<Metadata> {
  const { data } = await getAccount(params.id);

  let name = '';
  if (data?.name) name = data.name;
  if (!name && data) name = data._id.toString();

  const parentMetadata = await parent;
  const title = [name, parentMetadata.title?.absolute].filter((x) => !!x);

  return {
    title: title.join(' | '),
  };
}

export default function AccountIdLayout({ children }: Readonly<{ children: React.ReactNode }>) {
  return children;
}
