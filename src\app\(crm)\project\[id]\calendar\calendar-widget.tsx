import { EventItem } from '@/lib/calendar/event-item';
import { serializeCalendarEvents } from '@/lib/calendar/serialize';
import { MoreLink } from '@/lib/nav';
import { ListItem, NotFound, Sticky, Widget } from '@/lib/widget';
import { validateRequest } from '@/server';
import { getProject } from '@/server/projects';

interface CalendarWidgetProps {
  projectId: string;
}

export async function CalendarWidget({ projectId }: CalendarWidgetProps) {
  const { user } = await validateRequest();
  if (!user) return null;

  const { data: project } = await getProject(projectId);
  if (!project) return null;

  const { tzoffset } = user;

  const eventsToShow = serializeCalendarEvents(project, tzoffset || 0).slice(0, 5);

  return (
    <Widget label="Calendar">
      <Sticky
        bottom={
          <ListItem>
            <MoreLink href={`/project/${projectId}/calendar`}>List Events</MoreLink>
          </ListItem>
        }
      >
        {eventsToShow.length > 0 ? (
          eventsToShow.map((item) => (
            <ListItem key={item.id}>
              <EventItem projectId={projectId} item={item} tzoffset={tzoffset || 0} />
            </ListItem>
          ))
        ) : (
          <NotFound>No events available</NotFound>
        )}
      </Sticky>
    </Widget>
  );
}
