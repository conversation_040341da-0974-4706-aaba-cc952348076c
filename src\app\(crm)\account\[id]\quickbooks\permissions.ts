import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function (id: string) {
  'use server';

  const { user, account, isSystemAdmin } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not edit the Beacon connection.');

  let canEdit = await can(user, 'edit_account_service_quickbooks');
  canEdit = (account._id.equals(id) && canEdit) || isSystemAdmin;
  if (!canEdit) throw new ServerError('Forbidden: you can not edit the Beacon connection.');

  return { canEdit, user, account };
});
