import { notFound } from 'next/navigation';
import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { ProjectDoc } from '@/schemas';
import { ServerError } from '@/server';
import { getAccount } from '@/server/accounts';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function (project: ProjectDoc | null) {
  'use server';
  const { user, account, isSystemAdmin } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not edit projects.');

  const { googleKeyIsSet, googleApiKey } = await getAccount(account._id);

  const sameAccount = account._id.equals(project?.account) || isSystemAdmin;

  const canEdit = await can(user, 'edit_project', 'edit_customer', sameAccount);
  if (!canEdit) throw new ServerError('Forbidden: you can not edit projects.');

  const canDelete = await can(user, 'delete_project');

  if (!project?._id) notFound();

  return {
    canEdit,
    canDelete,
    user,
    account,
    project,
    canInputCoords: !googleKeyIsSet,
    googleApiKey,
  };
});
