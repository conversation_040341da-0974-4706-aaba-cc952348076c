import { notFound } from 'next/navigation';
import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { DeadLeadReasonDoc, ProjectDoc } from '@/schemas';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function (
  project: ProjectDoc | null,
  deadLeadReasons: DeadLeadReasonDoc[],
) {
  'use server';
  const { user, account, isSystemAdmin } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not edit project milestones.');

  const sameAccount = account._id.equals(project?.account) || isSystemAdmin;

  const canEdit = await can(user, 'edit_Project', sameAccount);
  if (!canEdit) throw new ServerError('Forbidden: you can not edit project milestones.');

  if (!project?._id) notFound();

  return { user, account, project, deadLeadReasons, canEdit };
});
