import { notFound } from 'next/navigation';
import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { ProjectDoc } from '@/schemas';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function (
  project: ProjectDoc | null,
  invoice?: ProjectDoc['invoices'][number],
) {
  'use server';
  const { user, account, isSystemAdmin } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not view this invoice.');

  const sameAccount = account._id.equals(project?.account) || isSystemAdmin;

  const canView = await can(user, 'view_project_invoices', sameAccount);
  if (!canView) throw new ServerError('Forbidden: you can not view this invoice.');

  const [viewUsers, canEdit, canInvoice, canScheduleDeliveryEvents, canScheduleLaborEvents] = await Promise.all([
    can(user, 'view_User'),
    can(user, 'edit_project_invoices', sameAccount, !project?.readOnly),
    can(user, 'create_project_invoice', sameAccount, !project?.readOnly),
    can(user, 'schedule_delivery_events'),
    can(user, 'schedule_labor_events'),
  ]);

  if (!project?._id) notFound();
  if (!invoice?._id) notFound();

  return {
    user,
    account,
    project,
    canView,
    invoice,
    viewUsers,
    canEdit,
    canInvoice,
    canScheduleDeliveryEvents,
    canScheduleLaborEvents,
  };
});
