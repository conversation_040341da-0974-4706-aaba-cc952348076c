'use server';

import mongoose from 'mongoose';
import { revalidatePath } from 'next/cache';

import { db } from '@/lib';
import { ActionResult } from '@/lib/form';
import { assertString } from '@/lib/validators';
import { getAccountModel } from '@/schemas';

import { validatePermissions } from './permissions';

export async function edit(id: string, _: unknown, formData: FormData): Promise<ActionResult> {
  'use server';

  try {
    await db();
    await validatePermissions(id);

    const accountModel = await getAccountModel();
    const account = await accountModel.findOne({ _id: new mongoose.mongo.ObjectId(id) });

    if (!account) throw new Error(`Account ${id} does not exist`);

    const fieldsToUpdate = {
      profileId: 'settings.beacon.profileId',
      username: 'settings.beacon.username',
      password: 'settings.beacon.password',
    };

    Object.entries(fieldsToUpdate).forEach(([key, path]) => {
      const value = formData.get(key);
      if (assertString(value)) account.set(path, value);
    });

    await account.save();
  } catch (e) {
    if (e instanceof Error) return { error: e.message };

    return { error: 'Unknown error' };
  }

  revalidatePath(`/account/${id}`);
  return { error: null };
}
