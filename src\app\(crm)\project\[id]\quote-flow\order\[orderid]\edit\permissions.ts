import { notFound } from 'next/navigation';
import { cache } from 'react';

import { can } from '@/lib/capabilities';
import { ProjectDoc } from '@/schemas';
import { ServerError } from '@/server';
import { validateRequest } from '@/server/auth';

export const validatePermissions = cache(async function (project: ProjectDoc | null, orderId: string) {
  'use server';

  const { user, account } = await validateRequest();
  if (!user || !account) throw new ServerError('Forbidden: you can not edit project orders.');

  const canDelete = await can(user, 'delete_project_orders');
  const canEditOrder = await can(user, 'edit_project_orders');

  if (!project?._id) notFound();

  const order = project.orders.find((o) => o.id === orderId);
  if (!order) notFound();

  if (order.readOnly || project.readOnly) throw new ServerError('Forbidden: you can not edit readonly orders.');

  return { user, account, project, order, canEdit: !project.readOnly && !order.readOnly, canDelete, canEditOrder };
});
