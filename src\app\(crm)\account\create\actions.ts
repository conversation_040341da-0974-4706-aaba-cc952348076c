import mongoose from 'mongoose';
import { redirect } from 'next/navigation';

import { db } from '@/lib';
import { ActionResult } from '@/lib/form';
import { getAccountModel, getUserModel, type User } from '@/schemas';

import { validatePermissions } from './permissions';

export async function create(_: unknown, formData: FormData): Promise<ActionResult> {
  'use server';
  let _id: mongoose.mongo.ObjectId | null = null;
  let user: User | null = null;

  try {
    ({ user } = await validatePermissions());
  } catch (error) {
    if (error instanceof Error) return { error: error.message };

    return { error: 'Unknown error' };
  }

  try {
    await db();

    const accountModel = await getAccountModel();

    const newAccount = new accountModel({
      name: formData.get('name') + '',
      prefix: formData.get('prefix') + '',
      domain: formData.get('domain') + '',
      settings: {},
    });

    await newAccount.save();

    if (!user.account && newAccount._id) {
      const userModel = await getUserModel();
      const userDoc = await userModel.findOne({ _id: user._id });

      if (userDoc) {
        userDoc.account = newAccount._id;
        await userDoc.save();
      }
    }

    _id = newAccount._id;
  } catch (e) {
    if (e instanceof Error) return { error: e.message };

    return { error: 'Unknown error' };
  }

  if (!_id) return { error: 'Failed to create record' };

  redirect(`/account/${_id}`);
}
