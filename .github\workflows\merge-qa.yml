name: 'Merge to QA'

env:
  GITHUB_TOKEN: ${{ secrets.TOKEN_GITHUB }}

on:
  push:
    branches:
      - development

jobs:
  merge-dev-to-qa:
    name: Merge Dev to QA
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        id: checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Check if version has been updated
        id: check
        uses: EndBug/version-check@v2
        with:
          token: ${{ env.GITHUB_TOKEN }}

      - name: Create a new version tag
        id: newtag
        uses: anothrNick/github-tag-action@v1
        if: steps.check.outputs.changed == 'true'
        env:
          CUSTOM_TAG: v${{ steps.check.outputs.version }}

      - name: Push into QA branch
        id: merge
        uses: mtanzi/action-automerge@v1
        if: steps.check.outputs.changed == 'true'
        with:
          github_token: ${{ env.GITHUB_TOKEN }}
          source: 'development'
          target: 'qa'
