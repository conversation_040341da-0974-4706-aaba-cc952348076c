'use server';

import mongoose from 'mongoose';
import { redirect } from 'next/navigation';

import { ActionResult } from '@/lib/form';
import { getTemplateFromFormData } from '@/lib/quote/mutate/save';
import { assertString } from '@/lib/validators';
import { getProjectModel } from '@/schemas';
import { PROJECT_ORDER_STATUS } from '@/schemas/projects/subdocuments';
import { validateEnum } from '@/server';

import { getData } from './helpers';

export async function edit(id: string, orderId: string, _: unknown, formData: FormData): Promise<ActionResult> {
  'use server';
  const { _action } = Object.fromEntries(formData);

  try {
    const { canDelete } = await getData(id, orderId);

    if (_action === 'delete') {
      if (!canDelete) throw new Error('Forbidden: you do not have permission to delete this order.');

      const projectModel = await getProjectModel();
      const project = await projectModel.findOne({ _id: new mongoose.mongo.ObjectId(id) });
      if (!project) throw new Error('No project found');

      const orders = project.orders.filter((order) => !order._id.equals(orderId));
      project.set('orders', orders);

      await project.save({ timestamps: false });
    } else {
      const projectModel = await getProjectModel();

      const project = await projectModel
        .findOne({ _id: new mongoose.mongo.ObjectId(id) })
        .populate('documents.location');
      if (!project) throw new Error('Not Found: project not found.');

      const order = project.orders.find((order) => order._id.equals(orderId));
      if (!order) throw new Error('Not Found: order was not found.');

      const form = Object.fromEntries(formData.entries());
      const name = form.name && assertString(form.name) ? form.name : '';
      const status = validateEnum(form.status, PROJECT_ORDER_STATUS) ? form.status : PROJECT_ORDER_STATUS.DRAFT;

      order.name = name;
      order.status = status;

      for (const quote of order.quotes) {
        await getTemplateFromFormData(formData, quote, quote._id.toString());
      }

      order.markModified('quotes');
      project.markModified('orders');
      await project.save({ timestamps: false });
    }
  } catch (e) {
    if (e instanceof Error) return { error: e.message };

    return { error: 'Unknown error' };
  }

  if (_action === 'delete') {
    redirect(`/project/${id}/quote-flow`);
  } else {
    redirect(`/project/${id}/quote-flow/order/${orderId}`);
  }
}
