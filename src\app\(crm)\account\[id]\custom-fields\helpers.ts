import { notFound } from 'next/navigation';
import { cache } from 'react';

import type { AccountDoc } from '@/schemas';
import { getAccount } from '@/server/accounts';
import { getLeadSources } from '@/server/lead-source';
import { getDeadLeadReasons } from '@/server/dead-lead-reason';
import { getLocations } from '@/server/locations';
import { getInsurances } from '@/server/insurance';

import { edit } from './actions';
import { validatePermissions } from './permissions';

export const getData = cache(async function (id: string) {
  'use server';

  let account: AccountDoc | null;
  let secrets: AccountDoc | null;
  let beaconIsSet = false;
  let quickBooksIsSet = false;
  const props = await validatePermissions(id);

  try {
    ({ data: account, secrets, beaconIsSet, quickBooksIsSet } = await getAccount(id, { includeSecrets: true }));
  } catch {
    notFound();
  }

  const deadLeadReasons = await getDeadLeadReasons(account._id.toString());
  const leadSources = await getLeadSources(account._id.toString());
  const locations = await getLocations(account._id.toString());
  const insurances = await getInsurances(account._id.toString());

  const editAction = edit.bind(null, id);

  return {
    ...props,
    currentAccount: props.account,
    account,
    editAction,
    secrets,
    beaconIsSet,
    quickBooksIsSet,
    deadLeadReasons: deadLeadReasons.data,
    leadSources: leadSources.data,
    locations: locations.data,
    insurances: insurances.data,
  };
});
