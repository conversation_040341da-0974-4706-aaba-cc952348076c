'use server';

import { cache } from 'react';

import { ServerError } from '@/server';
import { getProject } from '@/server/projects/get-project';
import { validateRequest } from '@/server/auth';
import { can } from '@/lib/capabilities';

export const getData = cache(async function (params: { id: string; eventId: string }) {
  const { user, account } = await validateRequest();

  if (!user || !account) throw new ServerError('Forbidden: you can not edit calendar events.');

  const { data: project } = await getProject(params.id);
  if (!project) return { error: 'Project not found' };

  const canScheduleDeliveryEvents = await can(user, 'schedule_delivery_events');
  const canScheduleLaborEvents = await can(user, 'schedule_labor_events');

  const deliveryEvent = project.deliveryEvents.find((event) => event.id === params.eventId);
  const laborEvent = project.laborEvents.find((event) => event.id === params.eventId);
  const appointment = project.appointments.find((appointment) => appointment.id === params.eventId);
  const canScheduleAppointments =
    (await can(user, 'schedule_appointments')) || appointment?.createdBy.toString() === user._id.toString();

  const canEdit = canScheduleDeliveryEvents || canScheduleLaborEvents || canScheduleAppointments;

  return {
    userTz: user.tzoffset,
    deliveryEvent,
    laborEvent,
    appointment,
    canEdit,
  };
});
