'use server';

import { COMMENT_TYPE } from '@/schemas/projects/subdocuments/comment/enums';
import { ActionResult } from '@/lib/form';

import { postComment, deleteComment } from './actions';

export async function postCommentAction(
  id: string,
  type: COMMENT_TYPE,
  prevState: unknown,
  formData: FormData,
): Promise<ActionResult> {
  return postComment(id, type, prevState, formData);
}

export async function deleteCommentAction(prevState: unknown, formData: FormData): Promise<ActionResult> {
  return deleteComment(prevState, formData);
}
