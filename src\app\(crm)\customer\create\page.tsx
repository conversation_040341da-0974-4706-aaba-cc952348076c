import { Metadata, ResolvingMetadata } from 'next';
import Link from 'next/link';

import { Field, FormButton } from '@/lib/form';
import { ActionFooter } from '@/lib/nav';
import { Widget } from '@/lib/widget';

import { create } from './actions';
import { validatePermissions } from './permissions';
import { CustomerForm } from './customer-form';
import SimilarCustomers from './similar-customers';

export async function generateMetadata(_: unknown, parent: ResolvingMetadata): Promise<Metadata> {
  const parentMetadata = await parent;

  return {
    title: 'Create | ' + parentMetadata.title?.absolute,
  };
}

export default async function CreatePage({
  searchParams,
}: {
  searchParams?: {
    error?: string;
    warning?: string;
    similarCustomers?: string;
    formData?: string;
    ignoreWarning?: string;
    duplicatePhone?: string;
  };
}) {
  await validatePermissions();

  // Parse similar customers from searchParams if they exist with error handling
  let similarCustomers = [];
  const ignoreWarning = searchParams?.ignoreWarning === 'true';

  if (searchParams?.similarCustomers && !ignoreWarning) {
    try {
      const decoded = decodeURIComponent(searchParams.similarCustomers);
      similarCustomers = JSON.parse(decoded);
      // Ensure it's an array
      if (!Array.isArray(similarCustomers)) {
        similarCustomers = [];
      }
    } catch (e) {
      // Log the error using a custom logging utility or handle it appropriately
      // Example: logError('Error parsing similarCustomers:', e);
      similarCustomers = [];
    }
  }

  // Parse form data from searchParams if it exists with error handling
  let formData = null;
  if (searchParams?.formData) {
    try {
      const decoded = decodeURIComponent(searchParams.formData);
      formData = JSON.parse(decoded);
    } catch (e) {
      // Log the error using a custom logging utility or handle it appropriately
      // Example: logError('Error parsing formData:', e);
      formData = null;
    }
  }

  // Parse error/warning from searchParams if they exist
  const error = searchParams?.error || null;
  const warning = searchParams?.warning || null;

  // Separate exact phone matches from similar customers
  const exactMatches = similarCustomers.filter((customer: { phoneNumbers?: string[] }) =>
    customer.phoneNumbers?.some((phone: string) => phone === formData?.phone?.replace(/\D/g, '')),
  );

  const potentialMatches = similarCustomers.filter(
    (customer: { phoneNumbers?: string[] }) =>
      !customer.phoneNumbers?.some((phone: string) => phone === formData?.phone?.replace(/\D/g, '')),
  );

  // Flag for duplicate phone detection
  const hasDuplicatePhone = searchParams?.duplicatePhone === 'true' || exactMatches.length > 0;

  // Only show a message if we have matches or a warning
  const showErrorMessage = error && !hasDuplicatePhone;
  const showDuplicateMessage = hasDuplicatePhone;
  const showWarningMessage = warning && !showDuplicateMessage;

  return (
    <div className="space-y-6">
      {showErrorMessage && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">{error}</div>
      )}

      {showDuplicateMessage && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          A customer with this phone number already exists. Please review the existing customers below.
        </div>
      )}

      {showWarningMessage && (
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-md">
          {warning}
          <p className="text-sm mt-2">
            You can continue creating this customer, but please review the similar customers below first.
          </p>
        </div>
      )}

      <CustomerForm className="space-y-6" action={create}>
        <Widget label="New Customer" className="space-y-4">
          <Field name="firstName" label="First Name" required value={formData?.firstName || ''} />
          <Field name="lastName" label="Last Name" required value={formData?.lastName || ''} />
          <Field name="email" label="Email" type="email" value={formData?.email || ''} />
          <Field name="phone" label="Phone" type="tel" required value={formData?.phone || ''} />

          {/* Hidden field to track if we should ignore similar customer warnings */}
          {searchParams?.warning && !exactMatches.length && <input type="hidden" name="ignoreWarning" value="true" />}
        </Widget>

        <ActionFooter left={<Link href="/customer">Back to Customers</Link>} right={<FormButton>Save</FormButton>} />
      </CustomerForm>

      {/* Display exact matches first if any */}
      {exactMatches.length > 0 && (
        <SimilarCustomers
          customers={exactMatches}
          title="Existing Customers"
          description="These customers have the exact same phone number you entered."
          titleClass="bg-red-100 dark:bg-red-800/20 border-red-400 dark:border-red-700/30 text-red-800 dark:text-red-300"
        />
      )}

      {/* Display potential matches if any */}
      {potentialMatches.length > 0 && (
        <SimilarCustomers
          customers={potentialMatches}
          title="Similar Customers"
          description="These customers have similar information to what you entered."
          titleClass="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-700/30"
        />
      )}
    </div>
  );
}
