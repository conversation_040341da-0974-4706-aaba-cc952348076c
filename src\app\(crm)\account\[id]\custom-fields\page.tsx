import { Metadata, ResolvingMetadata } from 'next';
import Link from 'next/link';

import { Body, Cell, Heading, Row, Table } from '@/lib/table';
import { validateRequest } from '@/server';
import { formatDate } from '@/lib/formatting';
import { WidgetEdit } from '@/lib/account';
import { Widget } from '@/lib/widget';
import { Button } from '@/lib/form';
import { Accordion, AccordionItem } from '@/lib/accordion/accordion';

import { getData } from './helpers';
import { EditPageProps } from './types';

export async function generateMetadata(_: unknown, parent: ResolvingMetadata): Promise<Metadata> {
  const parentMetadata = await parent;

  return {
    title: 'Custom Fields | ' + parentMetadata.title?.absolute,
  };
}

export default async function EditPage({ params }: Readonly<EditPageProps>) {
  const { account, leadSources, deadLeadReasons, locations, insurances } = await getData(params.id);
  const { user } = await validateRequest();

  return (
    <>
      <WidgetEdit label={account.name} id={account.id} readOnly>
        <Accordion>
          <AccordionItem id="lead-sources" title="Lead Sources">
            <Widget label=" " action={<Button href={`/lead-source/create`}>New</Button>}>
              <Table>
                <Heading>
                  <Cell>Name</Cell>
                  <Cell>Created</Cell>
                  <Cell>Action</Cell>
                </Heading>
                <Body>
                  {leadSources.map(async (item) => {
                    return (
                      <Row key={item.id}>
                        <Cell>{item.name}</Cell>

                        <Cell>{formatDate(item.created, true, user?.tzoffset)}</Cell>
                        <Cell>
                          <Link
                            className="text-orange-500 hover:underline cursor-pointer"
                            href={`/lead-source/${item.id}/edit`}
                          >
                            Edit
                          </Link>
                        </Cell>
                      </Row>
                    );
                  })}
                </Body>
              </Table>
            </Widget>
          </AccordionItem>
          <AccordionItem id="dead-lead-reasons" title="Dead Lead Reaons">
            <Widget label=" " action={<Button href={`/dead-lead-reason/create`}>New</Button>}>
              <Table>
                <Heading>
                  <Cell>Name</Cell>
                  <Cell>Created</Cell>
                  <Cell>Action</Cell>
                </Heading>
                <Body>
                  {deadLeadReasons.map(async (item) => {
                    return (
                      <Row key={item.id}>
                        <Cell>{item.name}</Cell>

                        <Cell>{formatDate(item.created, true, user?.tzoffset)}</Cell>
                        <Cell>
                          <Link
                            className="text-orange-500 hover:underline cursor-pointer"
                            href={`/dead-lead-reason/${item.id}/edit`}
                          >
                            Edit
                          </Link>
                        </Cell>
                      </Row>
                    );
                  })}
                </Body>
              </Table>
            </Widget>
          </AccordionItem>
          <AccordionItem id="locations" title="Locations">
            <Widget label=" " action={<Button href={`/location/create`}>New</Button>}>
              <Table>
                <Heading>
                  <Cell>Name</Cell>
                  <Cell>Created</Cell>
                  <Cell>Action</Cell>
                </Heading>
                <Body>
                  {locations.map(async (item) => {
                    return (
                      <Row key={item.id}>
                        <Cell>{item.name}</Cell>

                        <Cell>{formatDate(item.created, true, user?.tzoffset)}</Cell>
                        <Cell>
                          <Link
                            className="text-orange-500 hover:underline cursor-pointer"
                            href={`/location/${item.id}/edit`}
                          >
                            Edit
                          </Link>
                        </Cell>
                      </Row>
                    );
                  })}
                </Body>
              </Table>
            </Widget>
          </AccordionItem>
          <AccordionItem id="insurances" title="Insurances">
            <Widget label=" " action={<Button href={`/insurance/create`}>New</Button>}>
              <Table>
                <Heading>
                  <Cell>Name</Cell>
                  <Cell>Created</Cell>
                  <Cell>Action</Cell>
                </Heading>
                <Body>
                  {insurances?.map(async (item) => {
                    return (
                      <Row key={item.id}>
                        <Cell>{item.name}</Cell>

                        <Cell>{formatDate(item.created, true, user?.tzoffset)}</Cell>
                        <Cell>
                          <Link
                            className="text-orange-500 hover:underline cursor-pointer"
                            href={`/insurance/${item.id}/edit`}
                          >
                            Edit
                          </Link>
                        </Cell>
                      </Row>
                    );
                  })}
                </Body>
              </Table>
            </Widget>
          </AccordionItem>
        </Accordion>

        {/* <Widget label="Lead Sources" action={<Button href={`/lead-source/create`}>New</Button>}>
          <Table>
            <Heading>
              <Cell>Name</Cell>
              <Cell>Created</Cell>
              <Cell>Action</Cell>
            </Heading>
            <Body>
              {leadSources.map(async (item) => {
                return (
                  <Row key={item.id}>
                    <Cell>{item.name}</Cell>

                    <Cell>{formatDate(item.created, true, user?.tzoffset)}</Cell>
                    <Cell>
                      <Link
                        className="text-orange-500 hover:underline cursor-pointer"
                        href={`/lead-source/${item.id}/edit`}
                      >
                        Edit
                      </Link>
                    </Cell>
                  </Row>
                );
              })}
            </Body>
          </Table>
        </Widget> */}

        {/* <Widget label="Dead Lead Reasons" action={<Button href={`/dead-lead-reason/create`}>New</Button>}>
          <Table>
            <Heading>
              <Cell>Name</Cell>
              <Cell>Created</Cell>
              <Cell>Action</Cell>
            </Heading>
            <Body>
              {deadLeadReasons.map(async (item) => {
                return (
                  <Row key={item.id}>
                    <Cell>{item.name}</Cell>

                    <Cell>{formatDate(item.created, true, user?.tzoffset)}</Cell>
                    <Cell>
                      <Link
                        className="text-orange-500 hover:underline cursor-pointer"
                        href={`/dead-lead-reason/${item.id}/edit`}
                      >
                        Edit
                      </Link>
                    </Cell>
                  </Row>
                );
              })}
            </Body>
          </Table>
        </Widget> */}
      </WidgetEdit>
    </>
  );
}
