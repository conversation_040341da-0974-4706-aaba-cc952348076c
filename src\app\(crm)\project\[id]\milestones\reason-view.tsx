'use client';

import { Pencil } from 'lucide-react';
import { useEffect, useState } from 'react';

export function ReasonView({
  edit,
  view,
  canEdit,
  reset,
}: Readonly<{ edit: React.ReactNode; view: React.ReactNode; canEdit: boolean; reset: string }>) {
  const [isEditing, setEditing] = useState(false);

  useEffect(() => {
    setEditing(false);
  }, [reset]);

  if (isEditing) return edit;

  function onclick() {
    setEditing(true);
  }

  return (
    <div className="flex gap-4 items-center">
      {view}

      {!!canEdit && <Pencil className="h-4 w-4 cursor-pointer" onClick={onclick} />}
    </div>
  );
}
