import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { Argon2id } from 'oslo/password';

import type { ActionResult } from '@/lib/auth';
import { can } from '@/lib/capabilities';
import { assertString } from '@/lib/validators';
import type { AccountDoc } from '@/schemas';
import { TOKEN_TYPE } from '@/schemas/sessions/enums';
import { getUserModel } from '@/schemas/users';
import { luciaSession } from '@/server';
import { getAccountFromUrl } from '@/server/accounts';

export async function login(_: unknown, formData: FormData): Promise<ActionResult> {
  'use server';

  const email = formData.get('email');
  const password = formData.get('password');
  let redirectTo = '';

  let account: AccountDoc | undefined;

  try {
    ({ data: account } = await getAccountFromUrl());
  } catch {}

  if (!account) redirect('/register');

  try {
    const userModel = await getUserModel();
    const user = await userModel.findOne({ email }).select('+password');

    if (!user) throw new Error('Incorrect email or password');
    if (!user.verified)
      throw new Error(
        'Your account is not verified. Please verify via the link in the email or perform a password reset.',
      );

    if (!user.password || !assertString(password))
      throw new Error('Your account has not been assigned a password. Please reset your password.');

    const validPassword = await new Argon2id().verify(user.password, password);
    if (!validPassword) throw new Error('Incorrect email or password.');

    const canLogin = await can(user, 'login_web');
    if (!canLogin) throw new Error('You do not have access to this app');

    const accountExistsButNotLinked = !user.account && account?._id;
    if (accountExistsButNotLinked) {
      user.account = account._id;
      await user.save();
    }

    if (!user.account) redirectTo = '/account/create';

    const session = await luciaSession.createSession(user._id, { type: TOKEN_TYPE.COOKIE });
    const sessionCookie = luciaSession.createSessionCookie(session.id);
    cookies().set(sessionCookie.name, sessionCookie.value, sessionCookie.attributes);
  } catch (e) {
    if (e instanceof Error) return { error: e.message };

    return { error: 'Unknown error' };
  }

  if (redirectTo) redirect(redirectTo);
  return { error: null };
}
